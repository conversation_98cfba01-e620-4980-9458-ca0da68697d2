import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  Avatar,
  Chip,
  LinearProgress,
  Alert,
  Fab,
  Tooltip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  IconButton,
} from '@mui/material';
import {
  Add as AddIcon,
  School as SchoolIcon,
  Assignment as AssignmentIcon,
  TrendingUp as TrendingUpIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Speed as SpeedIcon,
  AutoAwesome as AutoAwesomeIcon,
  Visibility as VisibilityIcon,
  BarChart as BarChartIcon,
  Timeline as TimelineIcon,
} from '@mui/icons-material';

/**
 * Simple, Clear, and Professional Assignment Dashboard
 * 
 * Features:
 * 1. Clean overview of assignment statistics
 * 2. Quick action buttons for common tasks
 * 3. Recent assignments list
 * 4. Progress indicators
 * 5. Professional design with clear navigation
 */
const AssignmentDashboard = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalAssignments: 0,
    activeAssignments: 0,
    completedAssignments: 0,
    universities: 0,
    kpis: 0,
    completionRate: 0,
  });
  const [recentAssignments, setRecentAssignments] = useState([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // Mock data for now - replace with actual API calls
      const mockStats = {
        totalAssignments: 45,
        activeAssignments: 38,
        completedAssignments: 7,
        universities: 6,
        kpis: 12,
        completionRate: 84,
      };

      const mockRecentAssignments = [
        {
          id: 1,
          university: 'University of Gondar',
          kpi: 'Student Enrollment Rate',
          status: 'active',
          progress: 75,
          dueDate: '2024-12-31',
        },
        {
          id: 2,
          university: 'Addis Ababa University',
          kpi: 'Research Output Quality',
          status: 'completed',
          progress: 100,
          dueDate: '2024-11-30',
        },
        {
          id: 3,
          university: 'Bahir Dar University',
          kpi: 'Faculty Satisfaction Score',
          status: 'active',
          progress: 45,
          dueDate: '2024-12-15',
        },
      ];

      setStats(mockStats);
      setRecentAssignments(mockRecentAssignments);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'active': return 'primary';
      case 'overdue': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return <CheckCircleIcon />;
      case 'active': return <TrendingUpIcon />;
      case 'overdue': return <WarningIcon />;
      default: return <AssignmentIcon />;
    }
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <LinearProgress />
        <Typography variant="h6" sx={{ mt: 2, textAlign: 'center' }}>
          Loading assignment dashboard...
        </Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 600 }}>
          KPI Assignment Dashboard
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage and monitor KPI assignments across Ethiopian universities
        </Typography>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%', background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)' }}>
            <CardContent sx={{ color: 'white' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {stats.totalAssignments}
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Total Assignments
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                  <AssignmentIcon fontSize="large" />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%', background: 'linear-gradient(135deg, #388e3c 0%, #66bb6a 100%)' }}>
            <CardContent sx={{ color: 'white' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {stats.activeAssignments}
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Active Assignments
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                  <TrendingUpIcon fontSize="large" />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%', background: 'linear-gradient(135deg, #f57c00 0%, #ffb74d 100%)' }}>
            <CardContent sx={{ color: 'white' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {stats.universities}
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Universities
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                  <SchoolIcon fontSize="large" />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%', background: 'linear-gradient(135deg, #7b1fa2 0%, #ba68c8 100%)' }}>
            <CardContent sx={{ color: 'white' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {stats.completionRate}%
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Completion Rate
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                  <SpeedIcon fontSize="large" />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Quick Actions */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <AutoAwesomeIcon color="primary" />
                Quick Actions
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={4}>
                  <Button
                    fullWidth
                    variant="contained"
                    size="large"
                    startIcon={<AutoAwesomeIcon />}
                    onClick={() => navigate('/assignments/wizard')}
                    sx={{
                      py: 2,
                      background: 'linear-gradient(45deg, #1976d2 30%, #42a5f5 90%)',
                      boxShadow: '0 3px 5px 2px rgba(25, 118, 210, .3)',
                    }}
                  >
                    Smart Assignment Wizard
                  </Button>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <Button
                    fullWidth
                    variant="outlined"
                    size="large"
                    startIcon={<VisibilityIcon />}
                    onClick={() => navigate('/assignments')}
                    sx={{ py: 2 }}
                  >
                    View All Assignments
                  </Button>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <Button
                    fullWidth
                    variant="outlined"
                    size="large"
                    startIcon={<BarChartIcon />}
                    onClick={() => navigate('/reports')}
                    sx={{ py: 2 }}
                  >
                    View Reports
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                System Status
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Overall Progress
                </Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={stats.completionRate} 
                  sx={{ height: 8, borderRadius: 4 }}
                />
                <Typography variant="caption" color="text.secondary">
                  {stats.completionRate}% Complete
                </Typography>
              </Box>
              <Alert severity="info" sx={{ mt: 2 }}>
                All systems operational
              </Alert>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recent Assignments */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <TimelineIcon color="primary" />
            Recent Assignments
          </Typography>
          <List>
            {recentAssignments.map((assignment, index) => (
              <React.Fragment key={assignment.id}>
                <ListItem
                  sx={{
                    borderRadius: 1,
                    mb: 1,
                    '&:hover': { bgcolor: 'action.hover' }
                  }}
                >
                  <ListItemIcon>
                    <Avatar sx={{ bgcolor: getStatusColor(assignment.status) + '.main', width: 40, height: 40 }}>
                      {getStatusIcon(assignment.status)}
                    </Avatar>
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                          {assignment.university}
                        </Typography>
                        <Chip
                          size="small"
                          label={assignment.status}
                          color={getStatusColor(assignment.status)}
                          variant="outlined"
                        />
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          {assignment.kpi}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                          <LinearProgress
                            variant="determinate"
                            value={assignment.progress}
                            sx={{ flexGrow: 1, height: 6, borderRadius: 3 }}
                          />
                          <Typography variant="caption" color="text.secondary">
                            {assignment.progress}%
                          </Typography>
                        </Box>
                      </Box>
                    }
                  />
                  <IconButton
                    size="small"
                    onClick={() => navigate(`/assignments/${assignment.id}`)}
                  >
                    <VisibilityIcon />
                  </IconButton>
                </ListItem>
                {index < recentAssignments.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
          <Box sx={{ mt: 2, textAlign: 'center' }}>
            <Button
              variant="outlined"
              onClick={() => navigate('/assignments')}
              startIcon={<VisibilityIcon />}
            >
              View All Assignments
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Floating Action Button */}
      <Tooltip title="Create New Assignment">
        <Fab
          color="primary"
          sx={{
            position: 'fixed',
            bottom: 24,
            right: 24,
            background: 'linear-gradient(45deg, #1976d2 30%, #42a5f5 90%)',
            boxShadow: '0 4px 20px rgba(25, 118, 210, .3)',
          }}
          onClick={() => navigate('/assignments/wizard')}
        >
          <AutoAwesomeIcon />
        </Fab>
      </Tooltip>
    </Container>
  );
};

export default AssignmentDashboard;
