import api from './api'

/**
 * Get university performance data
 * @param {Object} params - Query parameters
 * @param {number} params.academic_year - Academic year ID
 * @param {number} params.theme - Theme ID (optional)
 * @returns {Promise<Array>} University performance data
 */
export const getUniversityPerformance = async (params = {}) => {
  try {
    const response = await api.get('/kpi-analysis/university_performance/', { params })
    return response.data
  } catch (error) {
    console.error('Error fetching university performance:', error)
    throw error
  }
}

/**
 * Get theme performance data
 * @param {Object} params - Query parameters
 * @param {number} params.academic_year - Academic year ID
 * @returns {Promise<Array>} Theme performance data
 */
export const getThemePerformance = async (params = {}) => {
  try {
    const response = await api.get('/kpi-analysis/theme_performance/', { params })
    return response.data
  } catch (error) {
    console.error('Error fetching theme performance:', error)
    throw error
  }
}

/**
 * Get performance trend data
 * @param {Object} params - Query parameters
 * @param {number} params.academic_year - Academic year ID
 * @param {number} params.theme - Theme ID (optional)
 * @returns {Promise<Array>} Performance trend data
 */
export const getPerformanceTrend = async (params = {}) => {
  try {
    const response = await api.get('/kpi-analysis/performance_trend/', { params })
    return response.data
  } catch (error) {
    console.error('Error fetching performance trend:', error)
    throw error
  }
}

/**
 * Get top performers
 * @param {Object} params - Query parameters
 * @param {number} params.academic_year - Academic year ID
 * @param {number} params.theme - Theme ID (optional)
 * @param {number} params.limit - Number of results to return (default: 5)
 * @param {string} params.level - Level of analysis (university, office, user) (default: university)
 * @returns {Promise<Array>} Top performers data
 */
export const getTopPerformers = async (params = {}) => {
  try {
    const response = await api.get('/kpi-analysis/top_performers/', { params })
    return response.data
  } catch (error) {
    console.error('Error fetching top performers:', error)
    throw error
  }
}

/**
 * Get low performers
 * @param {Object} params - Query parameters
 * @param {number} params.academic_year - Academic year ID
 * @param {number} params.theme - Theme ID (optional)
 * @param {number} params.limit - Number of results to return (default: 5)
 * @param {string} params.level - Level of analysis (university, office, user) (default: university)
 * @returns {Promise<Array>} Low performers data
 */
export const getLowPerformers = async (params = {}) => {
  try {
    const response = await api.get('/kpi-analysis/low_performers/', { params })
    return response.data
  } catch (error) {
    console.error('Error fetching low performers:', error)
    throw error
  }
}

/**
 * Get KPI distribution data
 * @param {Object} params - Query parameters
 * @param {number} params.academic_year - Academic year ID
 * @returns {Promise<Object>} KPI distribution data
 */
export const getKPIDistribution = async (params = {}) => {
  try {
    const response = await api.get('/kpi-analysis/kpi_distribution/', { params })
    return response.data
  } catch (error) {
    console.error('Error fetching KPI distribution:', error)
    throw error
  }
}

/**
 * Get office performance data
 * @param {Object} params - Query parameters
 * @param {number} params.academic_year - Academic year ID
 * @param {number} params.theme - Theme ID (optional)
 * @param {number} params.university - University ID (only in public schema)
 * @returns {Promise<Array>} Office performance data
 */
export const getOfficePerformance = async (params = {}) => {
  try {
    const response = await api.get('/kpi-analysis/office_performance/', { params })
    return response.data
  } catch (error) {
    console.error('Error fetching office performance:', error)
    throw error
  }
}

/**
 * Get user performance data
 * @param {Object} params - Query parameters
 * @param {number} params.academic_year - Academic year ID
 * @param {number} params.theme - Theme ID (optional)
 * @param {number} params.office - Office ID (required)
 * @param {number} params.university - University ID (only in public schema)
 * @returns {Promise<Array>} User performance data
 */
export const getUserPerformance = async (params = {}) => {
  try {
    const response = await api.get('/kpi-analysis/user_performance/', { params })
    return response.data
  } catch (error) {
    console.error('Error fetching user performance:', error)
    throw error
  }
}

/**
 * Get KPI comparison data across different levels
 * @param {Object} params - Query parameters
 * @param {number} params.academic_year - Academic year ID
 * @param {number} params.kpi - KPI ID
 * @param {number} params.university - University ID (only in public schema)
 * @returns {Promise<Object>} KPI comparison data
 */
export const getKPIComparison = async (params = {}) => {
  try {
    const response = await api.get('/kpi-analysis/kpi_comparison/', { params })
    return response.data
  } catch (error) {
    console.error('Error fetching KPI comparison:', error)
    throw error
  }
}

/**
 * Get all KPI analysis data for a dashboard
 * @param {Object} params - Query parameters
 * @param {number} params.academic_year - Academic year ID
 * @param {number} params.theme - Theme ID (optional)
 * @param {string} params.level - Level of analysis (university, office, user) (default: university)
 * @param {number} params.university - University ID (only in public schema)
 * @param {number} params.office - Office ID (only for user level)
 * @returns {Promise<Object>} All dashboard data
 */
export const getDashboardData = async (params = {}) => {
  try {
    // For now, let's create a simplified dashboard that works with the current data
    // We'll fetch what we can and provide fallbacks for the rest
    const level = params.level || 'university';

    // Try to fetch KPI distribution first as it's most likely to work
    let kpiDistribution = [];
    try {
      kpiDistribution = await getKPIDistribution(params);
    } catch (error) {
      console.warn('Failed to fetch KPI distribution:', error);
      // Provide fallback data
      kpiDistribution = [
        { name: 'Active', value: 0 },
        { name: 'Pending', value: 0 },
        { name: 'Completed', value: 0 },
        { name: 'Overdue', value: 0 }
      ];
    }

    // Create basic dashboard structure with fallback data
    const dashboardData = {
      performanceTrend: [
        { period: 'Q1', progress: 0 },
        { period: 'Q2', progress: 0 },
        { period: 'Q3', progress: 0 },
        { period: 'Q4', progress: 0 }
      ],
      kpiDistribution: kpiDistribution,
      topPerformers: [],
      lowPerformers: [],
      // Add basic metrics for the dashboard cards
      totalKPIs: 0,
      completedKPIs: 0,
      inProgressKPIs: 0,
      overdueKPIs: 0
    };

    // Try to fetch additional data based on level
    if (level === 'university') {
      try {
        const universityPerformance = await getUniversityPerformance(params);
        dashboardData.universityPerformance = universityPerformance;
      } catch (error) {
        console.warn('Failed to fetch university performance:', error);
        dashboardData.universityPerformance = [];
      }

      try {
        const themePerformance = await getThemePerformance(params);
        dashboardData.themePerformance = themePerformance;
      } catch (error) {
        console.warn('Failed to fetch theme performance:', error);
        dashboardData.themePerformance = [];
      }
    }

    // Calculate basic metrics from KPI distribution
    if (Array.isArray(kpiDistribution)) {
      dashboardData.totalKPIs = kpiDistribution.reduce((sum, item) => sum + (item.value || 0), 0);
      dashboardData.completedKPIs = kpiDistribution.find(item => item.name === 'Completed')?.value || 0;
      dashboardData.inProgressKPIs = kpiDistribution.find(item => item.name === 'Active')?.value || 0;
      dashboardData.overdueKPIs = kpiDistribution.find(item => item.name === 'Overdue')?.value || 0;
    }

    return dashboardData;
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    // Return a basic structure even if everything fails
    return {
      performanceTrend: [],
      kpiDistribution: [],
      topPerformers: [],
      lowPerformers: [],
      totalKPIs: 0,
      completedKPIs: 0,
      inProgressKPIs: 0,
      overdueKPIs: 0
    };
  }
}
