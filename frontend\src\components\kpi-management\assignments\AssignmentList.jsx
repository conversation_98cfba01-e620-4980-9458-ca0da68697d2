import { useState, useEffect } from 'react'
import { Link as RouterLink } from 'react-router-dom'
import { useAuthWithTenant } from '../../../hooks/useAuthWithTenant'
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  CircularProgress,
  Alert,
  Breadcrumbs,
  Link,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
} from '@mui/material'
import {
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as VisibilityIcon,
  Search as SearchIcon,
  Assignment as AssignmentIcon,
  School as SchoolIcon,
  BarChart as BarChartIcon,
  CalendarMonth as CalendarIcon,
  Home as HomeIcon,
  NavigateNext as NavigateNextIcon,
  AutoAwesome as AutoAwesomeIcon,
} from '@mui/icons-material'
import { getAssignments } from '../../../services/assignmentService'
import { getUniversities } from '../../../services/universityService'
import { getKPIs } from '../../../services/kpiService'
import { getAcademicYears } from '../../../services/academicYearService'
import { extractThemeSubtheme, extractAcademicYear } from '../../../utils/kpiAssignmentUtils'
import ExportAssignments from './ExportAssignments'

const AssignmentList = () => {
  const { isPublicTenant } = useAuthWithTenant();
  const [assignments, setAssignments] = useState([])
  const [universities, setUniversities] = useState([])
  const [kpis, setKpis] = useState([])
  const [academicYears, setAcademicYears] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [page, setPage] = useState(0)
  const [rowsPerPage, setRowsPerPage] = useState(10)
  const [searchQuery, setSearchQuery] = useState('')
  const [universityFilter, setUniversityFilter] = useState('')
  const [kpiFilter, setKpiFilter] = useState('')
  const [academicYearFilter, setAcademicYearFilter] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [totalCount, setTotalCount] = useState(0)

  useEffect(() => {
    const fetchFilters = async () => {
      try {
        console.log('University KPI Assignment - Fetching filters...');
        const [universitiesResponse, kpisResponse, academicYearsResponse] = await Promise.all([
          getUniversities({ page_size: 1000 }), // Increased to get all universities
          getKPIs({ page_size: 1000 }),         // Increased to get all KPIs
          getAcademicYears({ page_size: 100 }),
        ])

        console.log('University KPI Assignment - Filters fetched successfully');

        // Process and set the data
        const processedKpis = kpisResponse.results || [];
        const processedAcademicYears = academicYearsResponse.results || [];

        setUniversities(universitiesResponse.results || [])
        setKpis(processedKpis)
        setAcademicYears(processedAcademicYears)

        console.log('University KPI Assignment - Universities set in state:', (universitiesResponse.results || []).length, 'items');
        console.log('University KPI Assignment - KPIs set in state:', processedKpis.length, 'items');
        console.log('University KPI Assignment - Academic Years set in state:', processedAcademicYears.length, 'items');

        // Log some sample data for debugging
        if (processedKpis.length > 0) {
          console.log('University KPI Assignment - Sample KPI:', processedKpis[0]);
        }

        if (processedAcademicYears.length > 0) {
          console.log('University KPI Assignment - Sample Academic Year:', processedAcademicYears[0]);
        }
      } catch (err) {
        console.error('Error fetching filters:', err)
      }
    }

    fetchFilters()
  }, [])

  useEffect(() => {
    const fetchAssignments = async () => {
      try {
        setLoading(true)
        const params = {
          page: page + 1,
          page_size: rowsPerPage,
          search: searchQuery,
        }

        if (universityFilter) {
          params.university = universityFilter
        }

        if (kpiFilter) {
          params.kpi = kpiFilter
        }

        if (academicYearFilter) {
          params.academic_year = academicYearFilter
        }

        if (statusFilter) {
          params.status = statusFilter
        }

        const response = await getAssignments(params)
        setAssignments(response.results)
        setTotalCount(response.count)
        setError(null)
      } catch (err) {
        console.error('Error fetching assignments:', err)
        setError('Failed to load assignments. Please try again later.')
      } finally {
        setLoading(false)
      }
    }

    fetchAssignments()
  }, [page, rowsPerPage, searchQuery, universityFilter, kpiFilter, academicYearFilter, statusFilter])

  const handleChangePage = (event, newPage) => {
    setPage(newPage)
  }

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10))
    setPage(0)
  }

  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value)
    setPage(0)
  }

  const handleFilterChange = (event, filterSetter) => {
    filterSetter(event.target.value)
    setPage(0)
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Breadcrumbs
        separator={<NavigateNextIcon fontSize="small" />}
        aria-label="breadcrumb"
        sx={{ mb: 3 }}
      >
        <Link
          component={RouterLink}
          to="/dashboard"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Dashboard
        </Link>
        <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
          <AssignmentIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          KPI Assignments
        </Typography>
      </Breadcrumbs>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          KPI Assignments
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <ExportAssignments />
          <Button
            component={RouterLink}
            to="/assignments/wizard"
            variant="contained"
            color="primary"
            startIcon={<AutoAwesomeIcon />}
          >
            Smart Assignment Wizard
          </Button>
          <Button
            component={RouterLink}
            to="/assignments/create"
            variant="outlined"
            color="primary"
            startIcon={<AddIcon />}
          >
            Create Assignment
          </Button>
        </Box>
      </Box>

      {/* Description Section - Consistent with other main sections */}
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          KPI Assignment Management
        </Typography>
        <Typography variant="body1" paragraph>
          Manage and monitor KPI assignments across Ethiopian universities. Use the Smart Assignment Wizard
          for efficient bulk assignment creation, or create individual assignments as needed.
        </Typography>
        <Typography variant="body1">
          Filter and search assignments below to find specific assignments or track progress across universities.
        </Typography>
      </Paper>

      {/* Search and Filter Section */}
      <Paper sx={{ p: 3, mb: 4 }}>
        <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <TextField
            sx={{ flex: 1, minWidth: '250px' }}
            variant="outlined"
            placeholder="Search assignments..."
            value={searchQuery}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />

          <FormControl sx={{ minWidth: '200px' }}>
            <InputLabel id="university-filter-label">University</InputLabel>
            <Select
              labelId="university-filter-label"
              id="university-filter"
              value={universityFilter}
              label="University"
              onChange={(e) => handleFilterChange(e, setUniversityFilter)}
            >
              <MenuItem value="">
                <em>All Universities</em>
              </MenuItem>
              {universities.map((university) => (
                <MenuItem key={university.id} value={university.id}>
                  {university.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl sx={{ minWidth: '200px' }}>
            <InputLabel id="kpi-filter-label">KPI</InputLabel>
            <Select
              labelId="kpi-filter-label"
              id="kpi-filter"
              value={kpiFilter}
              label="KPI"
              onChange={(e) => handleFilterChange(e, setKpiFilter)}
            >
              <MenuItem value="">
                <em>All KPIs</em>
              </MenuItem>
              {kpis.map((kpi) => (
                <MenuItem key={kpi.id} value={kpi.id}>
                  {kpi.title}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl sx={{ minWidth: '200px' }}>
            <InputLabel id="academic-year-filter-label">Academic Year</InputLabel>
            <Select
              labelId="academic-year-filter-label"
              id="academic-year-filter"
              value={academicYearFilter}
              label="Academic Year"
              onChange={(e) => handleFilterChange(e, setAcademicYearFilter)}
            >
              <MenuItem value="">
                <em>All Years</em>
              </MenuItem>
              {academicYears.map((year) => (
                <MenuItem key={year.id} value={year.id}>
                  {year.name || year.year || 'Academic Year'} {year.is_current && '(Current)'}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl sx={{ minWidth: '150px' }}>
            <InputLabel id="status-filter-label">Status</InputLabel>
            <Select
              labelId="status-filter-label"
              id="status-filter"
              value={statusFilter}
              label="Status"
              onChange={(e) => handleFilterChange(e, setStatusFilter)}
            >
              <MenuItem value="">
                <em>All Statuses</em>
              </MenuItem>
              <MenuItem value="active">Active</MenuItem>
              <MenuItem value="pending">Pending</MenuItem>
              <MenuItem value="completed">Completed</MenuItem>
              <MenuItem value="archived">Archived</MenuItem>
            </Select>
          </FormControl>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>University</TableCell>
                <TableCell>Theme/Subtheme</TableCell>
                <TableCell>KPI</TableCell>
                <TableCell>Weight</TableCell>
                <TableCell>Current/Target</TableCell>
                <TableCell>Academic Year</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={8} align="center" sx={{ py: 3 }}>
                    <CircularProgress />
                  </TableCell>
                </TableRow>
              ) : assignments.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} align="center" sx={{ py: 3 }}>
                    <Typography variant="body1" color="text.secondary">
                      No assignments found.
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                assignments.map((assignment) => {
                  // Ensure university and kpi are objects
                  const university = assignment.university && typeof assignment.university === 'object'
                    ? assignment.university
                    : { id: assignment.university || 0, name: assignment.university_name || 'University' };

                  const kpi = assignment.kpi && typeof assignment.kpi === 'object'
                    ? assignment.kpi
                    : {
                        id: assignment.kpi || 0,
                        title: assignment.kpi_title || 'KPI',
                        code: assignment.kpi_code || '',
                        measurement_unit: assignment.measurement_unit_symbol
                          ? { symbol: assignment.measurement_unit_symbol }
                          : null
                      };

                  // DIRECT APPROACH: Extract theme and subtheme from the raw assignment data
                  console.log('Assignment for theme/subtheme extraction:', assignment);

                  // Extract theme directly from the assignment
                  let themeName = 'Unknown Theme';
                  let themeId = null;

                  // Try all possible theme sources in order of preference
                  if (assignment.kpi_theme_name) {
                    themeName = assignment.kpi_theme_name;
                    console.log('Found theme name in kpi_theme_name:', themeName);
                  } else if (assignment.theme_name) {
                    themeName = assignment.theme_name;
                    console.log('Found theme name in theme_name:', themeName);
                  } else if (assignment.kpi && assignment.kpi.theme_name) {
                    themeName = assignment.kpi.theme_name;
                    console.log('Found theme name in kpi.theme_name:', themeName);
                  } else if (assignment.kpi && assignment.kpi.theme && typeof assignment.kpi.theme === 'object' && assignment.kpi.theme.name) {
                    themeName = assignment.kpi.theme.name;
                    console.log('Found theme name in kpi.theme.name:', themeName);
                  }

                  // Extract subtheme directly from the assignment
                  let subthemeName = 'Unknown Subtheme';
                  let subthemeId = null;

                  // Try all possible subtheme sources in order of preference
                  if (assignment.kpi_sub_theme_name) {
                    subthemeName = assignment.kpi_sub_theme_name;
                    console.log('Found subtheme name in kpi_sub_theme_name:', subthemeName);
                  } else if (assignment.kpi_subtheme_name) {
                    subthemeName = assignment.kpi_subtheme_name;
                    console.log('Found subtheme name in kpi_subtheme_name:', subthemeName);
                  } else if (assignment.sub_theme_name) {
                    subthemeName = assignment.sub_theme_name;
                    console.log('Found subtheme name in sub_theme_name:', subthemeName);
                  } else if (assignment.subtheme_name) {
                    subthemeName = assignment.subtheme_name;
                    console.log('Found subtheme name in subtheme_name:', subthemeName);
                  } else if (assignment.kpi && assignment.kpi.sub_theme_name) {
                    subthemeName = assignment.kpi.sub_theme_name;
                    console.log('Found subtheme name in kpi.sub_theme_name:', subthemeName);
                  } else if (assignment.kpi && assignment.kpi.subtheme_name) {
                    subthemeName = assignment.kpi.subtheme_name;
                    console.log('Found subtheme name in kpi.subtheme_name:', subthemeName);
                  } else if (assignment.kpi && assignment.kpi.sub_theme && typeof assignment.kpi.sub_theme === 'object' && assignment.kpi.sub_theme.name) {
                    subthemeName = assignment.kpi.sub_theme.name;
                    console.log('Found subtheme name in kpi.sub_theme.name:', subthemeName);
                  } else if (assignment.kpi && assignment.kpi.subtheme && typeof assignment.kpi.subtheme === 'object' && assignment.kpi.subtheme.name) {
                    subthemeName = assignment.kpi.subtheme.name;
                    console.log('Found subtheme name in kpi.subtheme.name:', subthemeName);
                  }

                  const themeSubthemeText = `${themeName} / ${subthemeName}`;
                  console.log('Final theme/subtheme text:', themeSubthemeText);

                  // Get academic year directly
                  const academicYear = assignment.academic_year || {};
                  const academicYearText = academicYear.name || academicYear.year || 'N/A';

                  // Get target value
                  const targetValue = assignment.target_value || assignment.target || 0;

                  // Get measurement unit symbol
                  const measurementUnitSymbol = kpi.measurement_unit
                    ? kpi.measurement_unit.symbol
                    : assignment.measurement_unit_symbol || '';

                  // Get status
                  const status = assignment.status || (assignment.is_active ? 'active' : 'inactive');

                  // Get weight
                  const weight = assignment.weight || 0;

                  // Format current/target display
                  const currentTargetText = `${assignment.current_value !== null && assignment.current_value !== undefined
                    ? assignment.current_value
                    : 'N/A'} / ${targetValue} ${measurementUnitSymbol}`;

                  return (
                    <TableRow key={assignment.id} hover>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <SchoolIcon fontSize="small" sx={{ mr: 1, color: 'primary.main' }} />
                          <Link
                            component={RouterLink}
                            to={`/universities/${university.id}`}
                            underline="hover"
                            color="inherit"
                          >
                            {university.name}
                          </Link>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Tooltip title={`Theme: ${themeName}, Subtheme: ${subthemeName}`}>
                          <Typography variant="body2" noWrap sx={{ maxWidth: 200 }}>
                            {themeSubthemeText}
                          </Typography>
                        </Tooltip>
                      </TableCell>
                      <TableCell>
                        <Link
                          component={RouterLink}
                          to={`/kpis/${kpi.id}`}
                          underline="hover"
                          color="inherit"
                          sx={{ display: 'flex', alignItems: 'center' }}
                        >
                          <BarChartIcon fontSize="small" sx={{ mr: 1, color: 'secondary.main' }} />
                          {kpi.title}
                        </Link>
                      </TableCell>
                      <TableCell>
                        {weight}%
                      </TableCell>
                      <TableCell>
                        {currentTargetText}
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <CalendarIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                          {academicYearText}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={status}
                          size="small"
                          color={
                            status === 'active'
                              ? 'success'
                              : status === 'pending'
                              ? 'warning'
                              : status === 'completed'
                              ? 'primary'
                              : 'default'
                          }
                        />
                      </TableCell>
                      <TableCell>
                        <IconButton
                          component={RouterLink}
                          to={`/assignments/${assignment.id}`}
                          color="primary"
                          size="small"
                          title="View Assignment Details"
                        >
                          <VisibilityIcon />
                        </IconButton>
                        <IconButton
                          component={RouterLink}
                          to={`/assignments/${assignment.id}/edit`}
                          color="secondary"
                          size="small"
                          title="Edit Assignment"
                        >
                          <EditIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </TableContainer>

        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={totalCount}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>
    </Container>
  )
}

export default AssignmentList
