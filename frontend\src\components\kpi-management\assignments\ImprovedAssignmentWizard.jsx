import React, { useState, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Button,
  Grid,
  Card,
  CardContent,
  Avatar,
  Chip,
  Divider,
  Alert,
  LinearProgress,
  Autocomplete,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Tooltip,
  IconButton,
  Collapse,
} from '@mui/material';
import {
  Assignment as AssignmentIcon,
  School as SchoolIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
  Target as TargetIcon,
  Speed as SpeedIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';

/**
 * Improved KPI Assignment Wizard
 * 
 * Key Improvements:
 * 1. Simplified 2-step process instead of 3
 * 2. Smart defaults and auto-calculations
 * 3. Visual progress indicators
 * 4. Contextual help and guidance
 * 5. Real-time validation feedback
 * 6. Bulk assignment capabilities
 */
const ImprovedAssignmentWizard = ({ level = 'university', onComplete, onCancel }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    // Step 1: Entity & KPI Selection
    entities: [], // Universities, Offices, or Users
    kpi: null,
    academicYear: null,
    
    // Step 2: Smart Configuration
    distributionMode: 'equal', // equal, manual, template
    totalWeight: 100,
    assignments: [],
    
    // Advanced options (collapsible)
    useAdvancedOptions: false,
    globalTarget: '',
    globalBaseline: '',
    startDate: '',
    targetDate: '',
    notes: '',
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Step configuration
  const steps = [
    {
      label: 'Select Entities & KPI',
      description: 'Choose what to assign and to whom',
      icon: <AssignmentIcon />,
    },
    {
      label: 'Configure Assignments',
      description: 'Set targets, weights, and distribution',
      icon: <TargetIcon />,
    },
  ];

  // Smart weight distribution
  const distributeWeights = (mode, entities, totalWeight = 100) => {
    switch (mode) {
      case 'equal':
        const equalWeight = totalWeight / entities.length;
        return entities.map(entity => ({
          ...entity,
          weight: Math.round(equalWeight * 100) / 100,
          target: formData.globalTarget || '',
          baseline: formData.globalBaseline || '',
        }));
      
      case 'priority':
        // High priority gets more weight
        const priorityWeights = entities.map(entity => {
          switch (entity.priority) {
            case 'high': return 40;
            case 'medium': return 35;
            case 'low': return 25;
            default: return 33.33;
          }
        });
        const totalPriorityWeight = priorityWeights.reduce((sum, w) => sum + w, 0);
        return entities.map((entity, index) => ({
          ...entity,
          weight: Math.round((priorityWeights[index] / totalPriorityWeight) * totalWeight * 100) / 100,
          target: formData.globalTarget || '',
          baseline: formData.globalBaseline || '',
        }));
      
      default:
        return entities.map(entity => ({
          ...entity,
          weight: 0,
          target: '',
          baseline: '',
        }));
    }
  };

  // Validation
  const validateStep = (step) => {
    const newErrors = {};
    
    if (step === 0) {
      if (!formData.entities.length) {
        newErrors.entities = 'Please select at least one entity';
      }
      if (!formData.kpi) {
        newErrors.kpi = 'Please select a KPI';
      }
      if (!formData.academicYear) {
        newErrors.academicYear = 'Please select an academic year';
      }
    }
    
    if (step === 1) {
      const totalWeight = formData.assignments.reduce((sum, a) => sum + (a.weight || 0), 0);
      if (Math.abs(totalWeight - 100) > 0.01) {
        newErrors.weights = `Total weight must equal 100% (currently ${totalWeight}%)`;
      }
      
      formData.assignments.forEach((assignment, index) => {
        if (!assignment.target) {
          newErrors[`target_${index}`] = 'Target is required';
        }
      });
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(activeStep)) {
      if (activeStep === 0) {
        // Auto-generate assignments with smart defaults
        const assignments = distributeWeights(
          formData.distributionMode,
          formData.entities,
          formData.totalWeight
        );
        setFormData(prev => ({ ...prev, assignments }));
      }
      setActiveStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const handleSubmit = async () => {
    if (!validateStep(1)) return;
    
    setLoading(true);
    try {
      await onComplete(formData);
    } catch (error) {
      setErrors({ submit: error.message });
    } finally {
      setLoading(false);
    }
  };

  // Render step content
  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            {/* Entity Selection */}
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                      {level === 'university' ? <SchoolIcon /> : 
                       level === 'office' ? <BusinessIcon /> : <PersonIcon />}
                    </Avatar>
                    <Typography variant="h6">
                      Select {level === 'university' ? 'Universities' : 
                              level === 'office' ? 'Offices' : 'Users'}
                    </Typography>
                  </Box>
                  
                  <Autocomplete
                    multiple
                    options={[]} // Load from API
                    value={formData.entities}
                    onChange={(_, value) => setFormData(prev => ({ ...prev, entities: value }))}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label={`Select ${level === 'university' ? 'Universities' : 
                                        level === 'office' ? 'Offices' : 'Users'}`}
                        error={!!errors.entities}
                        helperText={errors.entities}
                      />
                    )}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip
                          variant="outlined"
                          label={option.name}
                          {...getTagProps({ index })}
                          key={option.id}
                        />
                      ))
                    }
                  />
                  
                  {formData.entities.length > 0 && (
                    <Alert severity="info" sx={{ mt: 2 }}>
                      {formData.entities.length} {level === 'university' ? 'universities' : 
                                                   level === 'office' ? 'offices' : 'users'} selected
                    </Alert>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* KPI & Academic Year Selection */}
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                      <AssignmentIcon />
                    </Avatar>
                    <Typography variant="h6">KPI & Academic Year</Typography>
                  </Box>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Autocomplete
                        options={[]} // Load from API
                        value={formData.kpi}
                        onChange={(_, value) => setFormData(prev => ({ ...prev, kpi: value }))}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="Select KPI"
                            error={!!errors.kpi}
                            helperText={errors.kpi}
                          />
                        )}
                      />
                    </Grid>
                    
                    <Grid item xs={12}>
                      <FormControl fullWidth error={!!errors.academicYear}>
                        <InputLabel>Academic Year</InputLabel>
                        <Select
                          value={formData.academicYear || ''}
                          onChange={(e) => setFormData(prev => ({ ...prev, academicYear: e.target.value }))}
                        >
                          {/* Load from API */}
                          <MenuItem value="2024-25">2024/25</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        );

      case 1:
        return (
          <Grid container spacing={3}>
            {/* Distribution Mode */}
            <Grid item xs={12}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>Weight Distribution</Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={4}>
                      <Button
                        variant={formData.distributionMode === 'equal' ? 'contained' : 'outlined'}
                        fullWidth
                        onClick={() => setFormData(prev => ({ ...prev, distributionMode: 'equal' }))}
                      >
                        Equal Distribution
                      </Button>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Button
                        variant={formData.distributionMode === 'priority' ? 'contained' : 'outlined'}
                        fullWidth
                        onClick={() => setFormData(prev => ({ ...prev, distributionMode: 'priority' }))}
                      >
                        Priority Based
                      </Button>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Button
                        variant={formData.distributionMode === 'manual' ? 'contained' : 'outlined'}
                        fullWidth
                        onClick={() => setFormData(prev => ({ ...prev, distributionMode: 'manual' }))}
                      >
                        Manual Entry
                      </Button>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Assignment Table */}
            <Grid item xs={12}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>Assignment Details</Typography>
                  {/* Assignment table would go here */}
                  <Alert severity="info">
                    Assignment configuration table will be implemented here
                  </Alert>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        );

      default:
        return null;
    }
  };

  return (
    <Container maxWidth="lg">
      <Paper sx={{ p: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" gutterBottom>
            Create KPI Assignment
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Streamlined process to assign KPIs efficiently
          </Typography>
        </Box>

        {/* Progress Stepper */}
        <Stepper activeStep={activeStep} orientation="vertical">
          {steps.map((step, index) => (
            <Step key={step.label}>
              <StepLabel
                optional={
                  <Typography variant="caption">{step.description}</Typography>
                }
                StepIconComponent={() => (
                  <Avatar
                    sx={{
                      bgcolor: index <= activeStep ? 'primary.main' : 'grey.300',
                      width: 32,
                      height: 32,
                    }}
                  >
                    {step.icon}
                  </Avatar>
                )}
              >
                {step.label}
              </StepLabel>
              <StepContent>
                {renderStepContent(index)}
                
                {/* Navigation Buttons */}
                <Box sx={{ mt: 3, mb: 2 }}>
                  <Button
                    disabled={index === 0}
                    onClick={handleBack}
                    sx={{ mr: 1 }}
                  >
                    Back
                  </Button>
                  <Button
                    variant="contained"
                    onClick={index === steps.length - 1 ? handleSubmit : handleNext}
                    disabled={loading}
                  >
                    {loading ? 'Processing...' : 
                     index === steps.length - 1 ? 'Create Assignments' : 'Next'}
                  </Button>
                  <Button
                    onClick={onCancel}
                    sx={{ ml: 1 }}
                  >
                    Cancel
                  </Button>
                </Box>
              </StepContent>
            </Step>
          ))}
        </Stepper>
      </Paper>
    </Container>
  );
};

export default ImprovedAssignmentWizard;
