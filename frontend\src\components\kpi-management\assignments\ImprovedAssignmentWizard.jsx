import React, { useState, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Button,
  Grid,
  Card,
  CardContent,
  Avatar,
  Chip,
  Divider,
  Alert,
  LinearProgress,
  Autocomplete,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Tooltip,
  IconButton,
  Collapse,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  InputAdornment,
  Slider,
  CircularProgress,
} from '@mui/material';
import {
  Assignment as AssignmentIcon,
  School as SchoolIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
  GpsFixed as TargetIcon,
  Speed as SpeedIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';

/**
 * Improved KPI Assignment Wizard
 *
 * Key Improvements:
 * 1. Simplified 2-step process instead of 3
 * 2. Smart defaults and auto-calculations
 * 3. Visual progress indicators
 * 4. Contextual help and guidance
 * 5. Real-time validation feedback
 * 6. Bulk assignment capabilities
 * 7. Proper weight distribution per university (each university gets 100% across their KPIs)
 */
const ImprovedAssignmentWizard = ({ level = 'university', onComplete, onCancel }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    // Step 1: Entity & KPI Selection
    entities: [], // Universities, Offices, or Users
    kpis: [], // Multiple KPIs can be selected
    academicYear: null,

    // Step 2: Smart Configuration
    distributionMode: 'equal', // equal, manual, template
    assignments: [], // Each assignment: { university, kpi, weight, target, baseline, ... }

    // Advanced options (collapsible)
    useAdvancedOptions: false,
    globalTarget: '',
    globalBaseline: '',
    startDate: '',
    targetDate: '',
    notes: '',
  });

  const [availableEntities, setAvailableEntities] = useState([]);
  const [availableKPIs, setAvailableKPIs] = useState([]);
  const [availableAcademicYears, setAvailableAcademicYears] = useState([]);
  const [loadingData, setLoadingData] = useState(false);

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      setLoadingData(true);
      try {
        // Mock data for now - replace with actual API calls
        const mockEntities = [
          { id: 1, name: 'University of Gondar', type: 'university', capacity: 'high' },
          { id: 2, name: 'Addis Ababa University', type: 'university', capacity: 'high' },
          { id: 3, name: 'Bahir Dar University', type: 'university', capacity: 'medium' },
          { id: 4, name: 'Jimma University', type: 'university', capacity: 'medium' },
          { id: 5, name: 'Hawassa University', type: 'university', capacity: 'medium' },
          { id: 6, name: 'Mekelle University', type: 'university', capacity: 'low' },
        ];

        const mockKPIs = [
          {
            id: 1,
            title: 'Student Enrollment Rate',
            measurement_unit: { symbol: '%' },
            priority: 'high',
            description: 'Percentage of enrolled students vs capacity'
          },
          {
            id: 2,
            title: 'Research Output Quality',
            measurement_unit: { symbol: 'Papers' },
            priority: 'high',
            description: 'Number of quality research publications'
          },
          {
            id: 3,
            title: 'Faculty Satisfaction Score',
            measurement_unit: { symbol: '%' },
            priority: 'medium',
            description: 'Faculty satisfaction survey results'
          },
          {
            id: 4,
            title: 'Graduate Employment Rate',
            measurement_unit: { symbol: '%' },
            priority: 'high',
            description: 'Percentage of graduates employed within 6 months'
          },
          {
            id: 5,
            title: 'International Collaboration',
            measurement_unit: { symbol: 'Projects' },
            priority: 'medium',
            description: 'Number of international research collaborations'
          },
          {
            id: 6,
            title: 'Community Engagement Score',
            measurement_unit: { symbol: 'Points' },
            priority: 'low',
            description: 'Community service and engagement activities'
          },
        ];

        const mockAcademicYears = [
          { id: 1, name: '2024/25', is_current: true, start_date: '2024-09-01', end_date: '2025-08-31' },
          { id: 2, name: '2023/24', is_current: false, start_date: '2023-09-01', end_date: '2024-08-31' },
          { id: 3, name: '2025/26', is_current: false, start_date: '2025-09-01', end_date: '2026-08-31' },
        ];

        setAvailableEntities(mockEntities);
        setAvailableKPIs(mockKPIs);
        setAvailableAcademicYears(mockAcademicYears);

        // Set current academic year as default
        const currentYear = mockAcademicYears.find(year => year.is_current);
        if (currentYear) {
          setFormData(prev => ({ ...prev, academicYear: currentYear }));
        }
      } catch (error) {
        setErrors({ load: 'Failed to load data. Please refresh and try again.' });
      } finally {
        setLoadingData(false);
      }
    };

    loadData();
  }, [level]);

  // Step configuration
  const steps = [
    {
      label: 'Select Entities & KPI',
      description: 'Choose what to assign and to whom',
      icon: <AssignmentIcon />,
    },
    {
      label: 'Configure Assignments',
      description: 'Set targets, weights, and distribution',
      icon: <TargetIcon />,
    },
  ];

  // Smart weight distribution for university-KPI assignments
  // Each university gets 100% weight distributed across their selected KPIs
  const distributeWeights = (mode, entities, kpis) => {
    if (!entities.length || !kpis.length) return [];

    const assignments = [];

    // For each university, create assignments for each KPI
    entities.forEach(entity => {
      let kpiWeights = [];

      switch (mode) {
        case 'equal':
          // Equal weight distribution across KPIs for this university
          const equalWeight = Math.round((100 / kpis.length) * 100) / 100;
          kpiWeights = kpis.map((kpi, index) => {
            // Adjust last KPI to ensure total equals exactly 100%
            const isLast = index === kpis.length - 1;
            const currentTotal = equalWeight * index;
            const adjustedWeight = isLast ? 100 - currentTotal : equalWeight;
            return adjustedWeight;
          });
          break;

        case 'priority':
          // Weight based on KPI priority
          const priorityWeights = kpis.map(kpi => {
            switch (kpi.priority || 'medium') {
              case 'high': return 40;
              case 'medium': return 35;
              case 'low': return 25;
              default: return 33.33;
            }
          });
          const totalPriority = priorityWeights.reduce((sum, w) => sum + w, 0);
          kpiWeights = priorityWeights.map(weight =>
            Math.round((weight / totalPriority) * 100 * 100) / 100
          );
          break;

        case 'capacity':
          // Weight based on university capacity and KPI priority combination
          const capacityMultiplier = entity.capacity === 'high' ? 1.2 : entity.capacity === 'medium' ? 1.0 : 0.8;
          const capacityPriorityWeights = kpis.map(kpi => {
            const basePriority = kpi.priority === 'high' ? 40 : kpi.priority === 'medium' ? 35 : 25;
            return basePriority * capacityMultiplier;
          });
          const totalCapacityPriority = capacityPriorityWeights.reduce((sum, w) => sum + w, 0);
          kpiWeights = capacityPriorityWeights.map(weight =>
            Math.round((weight / totalCapacityPriority) * 100 * 100) / 100
          );
          break;

        case 'manual':
          // Start with 0 weights for manual entry
          kpiWeights = kpis.map(() => 0);
          break;

        default:
          kpiWeights = kpis.map(() => 0);
      }

      // Create assignments for this university
      kpis.forEach((kpi, kpiIndex) => {
        assignments.push({
          university: entity,
          kpi: kpi,
          weight: kpiWeights[kpiIndex],
          target: formData.globalTarget || '',
          baseline: formData.globalBaseline || '',
          min_value: '',
          max_value: '',
          notes: '',
          // Unique identifier for the assignment
          id: `${entity.id}-${kpi.id}`,
        });
      });
    });

    return assignments;
  };

  // Update assignment weight
  const updateAssignmentWeight = (index, newWeight) => {
    const updatedAssignments = [...formData.assignments];
    updatedAssignments[index].weight = parseFloat(newWeight) || 0;
    setFormData(prev => ({ ...prev, assignments: updatedAssignments }));
  };

  // Update assignment field
  const updateAssignmentField = (index, field, value) => {
    const updatedAssignments = [...formData.assignments];
    updatedAssignments[index][field] = value;
    setFormData(prev => ({ ...prev, assignments: updatedAssignments }));
  };

  // Calculate total weight per university (each should be 100%)
  const getUniversityWeights = () => {
    const universityWeights = {};

    formData.assignments.forEach(assignment => {
      const universityId = assignment.university.id;
      if (!universityWeights[universityId]) {
        universityWeights[universityId] = {
          university: assignment.university,
          totalWeight: 0,
          assignments: []
        };
      }
      universityWeights[universityId].totalWeight += assignment.weight || 0;
      universityWeights[universityId].assignments.push(assignment);
    });

    return universityWeights;
  };

  // Check if all universities have valid weight distribution
  const isWeightDistributionValid = () => {
    const universityWeights = getUniversityWeights();
    return Object.values(universityWeights).every(
      univ => Math.abs(univ.totalWeight - 100) < 0.01
    );
  };

  // Validation
  const validateStep = (step) => {
    const newErrors = {};

    if (step === 0) {
      if (!formData.entities.length) {
        newErrors.entities = 'Please select at least one university';
      }
      if (!formData.kpis.length) {
        newErrors.kpis = 'Please select at least one KPI';
      }
      if (!formData.academicYear) {
        newErrors.academicYear = 'Please select an academic year';
      }
    }

    if (step === 1) {
      // Validate weight distribution per university
      const universityWeights = getUniversityWeights();
      const invalidUniversities = [];

      Object.values(universityWeights).forEach(univ => {
        if (Math.abs(univ.totalWeight - 100) > 0.01) {
          invalidUniversities.push(`${univ.university.name}: ${univ.totalWeight.toFixed(2)}%`);
        }
      });

      if (invalidUniversities.length > 0) {
        newErrors.weights = `Each university must have 100% weight distribution. Issues: ${invalidUniversities.join(', ')}`;
      }

      // Validate targets
      formData.assignments.forEach((assignment, index) => {
        if (!assignment.target) {
          newErrors[`target_${index}`] = 'Target is required';
        }
      });
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(activeStep)) {
      if (activeStep === 0) {
        // Auto-generate assignments with smart defaults
        const assignments = distributeWeights(
          formData.distributionMode,
          formData.entities,
          formData.kpis
        );
        setFormData(prev => ({ ...prev, assignments }));
      }
      setActiveStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const handleSubmit = async () => {
    if (!validateStep(1)) return;
    
    setLoading(true);
    try {
      await onComplete(formData);
    } catch (error) {
      setErrors({ submit: error.message });
    } finally {
      setLoading(false);
    }
  };

  // Render step content
  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            {/* Entity Selection */}
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                      {level === 'university' ? <SchoolIcon /> : 
                       level === 'office' ? <BusinessIcon /> : <PersonIcon />}
                    </Avatar>
                    <Typography variant="h6">
                      Select {level === 'university' ? 'Universities' : 
                              level === 'office' ? 'Offices' : 'Users'}
                    </Typography>
                  </Box>
                  
                  <Autocomplete
                    multiple
                    options={[]} // Load from API
                    value={formData.entities}
                    onChange={(_, value) => setFormData(prev => ({ ...prev, entities: value }))}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label={`Select ${level === 'university' ? 'Universities' : 
                                        level === 'office' ? 'Offices' : 'Users'}`}
                        error={!!errors.entities}
                        helperText={errors.entities}
                      />
                    )}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip
                          variant="outlined"
                          label={option.name}
                          {...getTagProps({ index })}
                          key={option.id}
                        />
                      ))
                    }
                  />
                  
                  {formData.entities.length > 0 && (
                    <Alert severity="info" sx={{ mt: 2 }}>
                      {formData.entities.length} {level === 'university' ? 'universities' : 
                                                   level === 'office' ? 'offices' : 'users'} selected
                    </Alert>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* KPI & Academic Year Selection */}
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                      <AssignmentIcon />
                    </Avatar>
                    <Typography variant="h6">KPI & Academic Year</Typography>
                  </Box>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Autocomplete
                        multiple
                        options={availableKPIs}
                        value={formData.kpis}
                        onChange={(_, value) => setFormData(prev => ({ ...prev, kpis: value }))}
                        getOptionLabel={(option) => option.title}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="Select KPIs"
                            error={!!errors.kpis}
                            helperText={errors.kpis || "Select one or more KPIs to assign"}
                          />
                        )}
                        renderTags={(value, getTagProps) =>
                          value.map((option, index) => (
                            <Chip
                              variant="outlined"
                              label={option.title}
                              {...getTagProps({ index })}
                              key={option.id}
                            />
                          ))
                        }
                      />
                    </Grid>
                    
                    <Grid item xs={12}>
                      <FormControl fullWidth error={!!errors.academicYear}>
                        <InputLabel>Academic Year</InputLabel>
                        <Select
                          value={formData.academicYear || ''}
                          onChange={(e) => setFormData(prev => ({ ...prev, academicYear: e.target.value }))}
                        >
                          {/* Load from API */}
                          <MenuItem value="2024-25">2024/25</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        );

      case 1:
        const universityWeights = getUniversityWeights();
        const isWeightValid = isWeightDistributionValid();

        return (
          <Grid container spacing={3}>
            {/* Distribution Mode */}
            <Grid item xs={12}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>Weight Distribution Mode</Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={4}>
                      <Button
                        variant={formData.distributionMode === 'equal' ? 'contained' : 'outlined'}
                        fullWidth
                        onClick={() => {
                          setFormData(prev => ({ ...prev, distributionMode: 'equal' }));
                          const newAssignments = distributeWeights('equal', formData.entities, formData.kpis);
                          setFormData(prev => ({ ...prev, assignments: newAssignments }));
                        }}
                        startIcon={<SpeedIcon />}
                      >
                        Equal Distribution
                      </Button>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Button
                        variant={formData.distributionMode === 'priority' ? 'contained' : 'outlined'}
                        fullWidth
                        onClick={() => {
                          setFormData(prev => ({ ...prev, distributionMode: 'priority' }));
                          const newAssignments = distributeWeights('priority', formData.entities, formData.kpis);
                          setFormData(prev => ({ ...prev, assignments: newAssignments }));
                        }}
                        startIcon={<TargetIcon />}
                      >
                        Priority Based
                      </Button>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Button
                        variant={formData.distributionMode === 'manual' ? 'contained' : 'outlined'}
                        fullWidth
                        onClick={() => {
                          setFormData(prev => ({ ...prev, distributionMode: 'manual' }));
                          const newAssignments = distributeWeights('manual', formData.entities, formData.kpis);
                          setFormData(prev => ({ ...prev, assignments: newAssignments }));
                        }}
                        startIcon={<AssignmentIcon />}
                      >
                        Manual Entry
                      </Button>
                    </Grid>
                  </Grid>

                  {/* Weight Summary */}
                  <Box sx={{ mt: 3, p: 2, bgcolor: isWeightValid ? 'success.light' : 'warning.light', borderRadius: 1 }}>
                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      {isWeightValid ? <CheckCircleIcon color="success" /> : <WarningIcon color="warning" />}
                      Weight Distribution Status: {isWeightValid ? 'All universities have 100% ✓' : 'Issues detected'}
                    </Typography>
                    {Object.values(universityWeights).map(univ => (
                      <Typography key={univ.university.id} variant="caption" display="block" sx={{ ml: 3 }}>
                        {univ.university.name}: {univ.totalWeight.toFixed(2)}%
                        {Math.abs(univ.totalWeight - 100) < 0.01 ? ' ✓' : ' ⚠️'}
                      </Typography>
                    ))}
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Assignment Table */}
            <Grid item xs={12}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>Assignment Configuration</Typography>

                  {formData.assignments.length > 0 ? (
                    <TableContainer>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>University</TableCell>
                            <TableCell>KPI</TableCell>
                            <TableCell>Weight (%)</TableCell>
                            <TableCell>Target</TableCell>
                            <TableCell>Baseline</TableCell>
                            <TableCell>Notes</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {formData.assignments.map((assignment, index) => (
                            <TableRow key={assignment.id}>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
                                    <SchoolIcon />
                                  </Avatar>
                                  {assignment.university.name}
                                </Box>
                              </TableCell>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <Avatar sx={{ width: 32, height: 32, bgcolor: 'secondary.main' }}>
                                    <AssignmentIcon />
                                  </Avatar>
                                  <Box>
                                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                      {assignment.kpi.title}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                      {assignment.kpi.measurement_unit?.symbol || ''}
                                    </Typography>
                                  </Box>
                                </Box>
                              </TableCell>
                              <TableCell>
                                <TextField
                                  type="number"
                                  value={assignment.weight || ''}
                                  onChange={(e) => updateAssignmentWeight(index, e.target.value)}
                                  size="small"
                                  inputProps={{ min: 0, max: 100, step: 0.01 }}
                                  InputProps={{
                                    endAdornment: <InputAdornment position="end">%</InputAdornment>
                                  }}
                                  sx={{ width: 100 }}
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  value={assignment.target || ''}
                                  onChange={(e) => updateAssignmentField(index, 'target', e.target.value)}
                                  size="small"
                                  placeholder="Target value"
                                  InputProps={{
                                    endAdornment: assignment.kpi?.measurement_unit?.symbol && (
                                      <InputAdornment position="end">
                                        {assignment.kpi.measurement_unit.symbol}
                                      </InputAdornment>
                                    )
                                  }}
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  value={assignment.baseline || ''}
                                  onChange={(e) => updateAssignmentField(index, 'baseline', e.target.value)}
                                  size="small"
                                  placeholder="Baseline"
                                  InputProps={{
                                    endAdornment: assignment.kpi?.measurement_unit?.symbol && (
                                      <InputAdornment position="end">
                                        {assignment.kpi.measurement_unit.symbol}
                                      </InputAdornment>
                                    )
                                  }}
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  value={assignment.notes || ''}
                                  onChange={(e) => updateAssignmentField(index, 'notes', e.target.value)}
                                  size="small"
                                  placeholder="Optional notes"
                                  multiline
                                  maxRows={2}
                                />
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  ) : (
                    <Alert severity="info">
                      Please select entities and a KPI in the previous step to configure assignments.
                    </Alert>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Advanced Options */}
            <Grid item xs={12}>
              <Card variant="outlined">
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Typography variant="h6">Advanced Options</Typography>
                    <IconButton onClick={() => setShowAdvanced(!showAdvanced)}>
                      {showAdvanced ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </IconButton>
                  </Box>

                  <Collapse in={showAdvanced}>
                    <Grid container spacing={2} sx={{ mt: 1 }}>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          label="Global Target"
                          value={formData.globalTarget}
                          onChange={(e) => setFormData(prev => ({ ...prev, globalTarget: e.target.value }))}
                          fullWidth
                          size="small"
                          helperText="Apply this target to all assignments"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          label="Global Baseline"
                          value={formData.globalBaseline}
                          onChange={(e) => setFormData(prev => ({ ...prev, globalBaseline: e.target.value }))}
                          fullWidth
                          size="small"
                          helperText="Apply this baseline to all assignments"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          label="Start Date"
                          type="date"
                          value={formData.startDate}
                          onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                          fullWidth
                          size="small"
                          InputLabelProps={{ shrink: true }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          label="Target Date"
                          type="date"
                          value={formData.targetDate}
                          onChange={(e) => setFormData(prev => ({ ...prev, targetDate: e.target.value }))}
                          fullWidth
                          size="small"
                          InputLabelProps={{ shrink: true }}
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          label="Notes"
                          value={formData.notes}
                          onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                          fullWidth
                          multiline
                          rows={3}
                          size="small"
                          helperText="Additional notes for all assignments"
                        />
                      </Grid>
                    </Grid>
                  </Collapse>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        );

      default:
        return null;
    }
  };

  return (
    <Container maxWidth="lg">
      <Paper sx={{ p: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" gutterBottom>
            Create KPI Assignment
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Streamlined process to assign KPIs efficiently
          </Typography>
        </Box>

        {/* Progress Stepper */}
        <Stepper activeStep={activeStep} orientation="vertical">
          {steps.map((step, index) => (
            <Step key={step.label}>
              <StepLabel
                optional={
                  <Typography variant="caption">{step.description}</Typography>
                }
                StepIconComponent={() => (
                  <Avatar
                    sx={{
                      bgcolor: index <= activeStep ? 'primary.main' : 'grey.300',
                      width: 32,
                      height: 32,
                    }}
                  >
                    {step.icon}
                  </Avatar>
                )}
              >
                {step.label}
              </StepLabel>
              <StepContent>
                {renderStepContent(index)}
                
                {/* Navigation Buttons */}
                <Box sx={{ mt: 3, mb: 2 }}>
                  <Button
                    disabled={index === 0}
                    onClick={handleBack}
                    sx={{ mr: 1 }}
                  >
                    Back
                  </Button>
                  <Button
                    variant="contained"
                    onClick={index === steps.length - 1 ? handleSubmit : handleNext}
                    disabled={loading}
                  >
                    {loading ? 'Processing...' : 
                     index === steps.length - 1 ? 'Create Assignments' : 'Next'}
                  </Button>
                  <Button
                    onClick={onCancel}
                    sx={{ ml: 1 }}
                  >
                    Cancel
                  </Button>
                </Box>
              </StepContent>
            </Step>
          ))}
        </Stepper>
      </Paper>
    </Container>
  );
};

export default ImprovedAssignmentWizard;
