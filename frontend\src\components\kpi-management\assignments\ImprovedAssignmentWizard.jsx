import React, { useState, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Button,
  Grid,
  Card,
  CardContent,
  Avatar,
  Chip,
  Divider,
  Alert,
  LinearProgress,
  Autocomplete,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Tooltip,
  IconButton,
  Collapse,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  InputAdornment,
  Slider,
  CircularProgress,
} from '@mui/material';
import {
  Assignment as AssignmentIcon,
  School as SchoolIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
  Target as TargetIcon,
  Speed as SpeedIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';

/**
 * Improved KPI Assignment Wizard
 * 
 * Key Improvements:
 * 1. Simplified 2-step process instead of 3
 * 2. Smart defaults and auto-calculations
 * 3. Visual progress indicators
 * 4. Contextual help and guidance
 * 5. Real-time validation feedback
 * 6. Bulk assignment capabilities
 */
const ImprovedAssignmentWizard = ({ level = 'university', onComplete, onCancel }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    // Step 1: Entity & KPI Selection
    entities: [], // Universities, Offices, or Users
    kpi: null,
    academicYear: null,

    // Step 2: Smart Configuration
    distributionMode: 'equal', // equal, manual, template
    totalWeight: 100,
    assignments: [],

    // Advanced options (collapsible)
    useAdvancedOptions: false,
    globalTarget: '',
    globalBaseline: '',
    startDate: '',
    targetDate: '',
    notes: '',
  });

  const [availableEntities, setAvailableEntities] = useState([]);
  const [availableKPIs, setAvailableKPIs] = useState([]);
  const [availableAcademicYears, setAvailableAcademicYears] = useState([]);
  const [loadingData, setLoadingData] = useState(false);

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      setLoadingData(true);
      try {
        // Mock data for now - replace with actual API calls
        const mockEntities = [
          { id: 1, name: 'University of Gondar', type: 'university' },
          { id: 2, name: 'Addis Ababa University', type: 'university' },
          { id: 3, name: 'Bahir Dar University', type: 'university' },
        ];

        const mockKPIs = [
          { id: 1, title: 'Student Enrollment Rate', measurement_unit: { symbol: '%' } },
          { id: 2, title: 'Research Output Quality', measurement_unit: { symbol: '#' } },
          { id: 3, title: 'Faculty Satisfaction Score', measurement_unit: { symbol: '%' } },
        ];

        const mockAcademicYears = [
          { id: 1, name: '2024/25', is_current: true },
          { id: 2, name: '2023/24', is_current: false },
        ];

        setAvailableEntities(mockEntities);
        setAvailableKPIs(mockKPIs);
        setAvailableAcademicYears(mockAcademicYears);

        // Set current academic year as default
        const currentYear = mockAcademicYears.find(year => year.is_current);
        if (currentYear) {
          setFormData(prev => ({ ...prev, academicYear: currentYear }));
        }
      } catch (error) {
        setErrors({ load: 'Failed to load data. Please refresh and try again.' });
      } finally {
        setLoadingData(false);
      }
    };

    loadData();
  }, [level]);

  // Step configuration
  const steps = [
    {
      label: 'Select Entities & KPI',
      description: 'Choose what to assign and to whom',
      icon: <AssignmentIcon />,
    },
    {
      label: 'Configure Assignments',
      description: 'Set targets, weights, and distribution',
      icon: <TargetIcon />,
    },
  ];

  // Smart weight distribution
  const distributeWeights = (mode, entities, totalWeight = 100) => {
    if (!entities.length) return [];

    switch (mode) {
      case 'equal':
        const equalWeight = Math.round((totalWeight / entities.length) * 100) / 100;
        return entities.map((entity, index) => {
          // Adjust last entity to ensure total equals exactly 100%
          const isLast = index === entities.length - 1;
          const currentTotal = equalWeight * index;
          const adjustedWeight = isLast ? totalWeight - currentTotal : equalWeight;

          return {
            ...entity,
            weight: adjustedWeight,
            target: formData.globalTarget || '',
            baseline: formData.globalBaseline || '',
            min_value: '',
            max_value: '',
            notes: '',
          };
        });

      case 'capacity':
        // Weight based on entity capacity (mock logic)
        const capacityWeights = entities.map(entity => {
          // Mock capacity calculation - replace with real logic
          return Math.random() * 50 + 25; // Random between 25-75
        });
        const totalCapacity = capacityWeights.reduce((sum, w) => sum + w, 0);

        return entities.map((entity, index) => ({
          ...entity,
          weight: Math.round((capacityWeights[index] / totalCapacity) * totalWeight * 100) / 100,
          target: formData.globalTarget || '',
          baseline: formData.globalBaseline || '',
          min_value: '',
          max_value: '',
          notes: '',
        }));

      case 'manual':
        return entities.map(entity => ({
          ...entity,
          weight: 0,
          target: '',
          baseline: '',
          min_value: '',
          max_value: '',
          notes: '',
        }));

      default:
        return entities.map(entity => ({
          ...entity,
          weight: 0,
          target: '',
          baseline: '',
          min_value: '',
          max_value: '',
          notes: '',
        }));
    }
  };

  // Update assignment weight
  const updateAssignmentWeight = (index, newWeight) => {
    const updatedAssignments = [...formData.assignments];
    updatedAssignments[index].weight = parseFloat(newWeight) || 0;
    setFormData(prev => ({ ...prev, assignments: updatedAssignments }));
  };

  // Update assignment field
  const updateAssignmentField = (index, field, value) => {
    const updatedAssignments = [...formData.assignments];
    updatedAssignments[index][field] = value;
    setFormData(prev => ({ ...prev, assignments: updatedAssignments }));
  };

  // Calculate total weight
  const getTotalWeight = () => {
    return formData.assignments.reduce((sum, assignment) => sum + (assignment.weight || 0), 0);
  };

  // Validation
  const validateStep = (step) => {
    const newErrors = {};
    
    if (step === 0) {
      if (!formData.entities.length) {
        newErrors.entities = 'Please select at least one entity';
      }
      if (!formData.kpi) {
        newErrors.kpi = 'Please select a KPI';
      }
      if (!formData.academicYear) {
        newErrors.academicYear = 'Please select an academic year';
      }
    }
    
    if (step === 1) {
      const totalWeight = formData.assignments.reduce((sum, a) => sum + (a.weight || 0), 0);
      if (Math.abs(totalWeight - 100) > 0.01) {
        newErrors.weights = `Total weight must equal 100% (currently ${totalWeight}%)`;
      }
      
      formData.assignments.forEach((assignment, index) => {
        if (!assignment.target) {
          newErrors[`target_${index}`] = 'Target is required';
        }
      });
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(activeStep)) {
      if (activeStep === 0) {
        // Auto-generate assignments with smart defaults
        const assignments = distributeWeights(
          formData.distributionMode,
          formData.entities,
          formData.totalWeight
        );
        setFormData(prev => ({ ...prev, assignments }));
      }
      setActiveStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const handleSubmit = async () => {
    if (!validateStep(1)) return;
    
    setLoading(true);
    try {
      await onComplete(formData);
    } catch (error) {
      setErrors({ submit: error.message });
    } finally {
      setLoading(false);
    }
  };

  // Render step content
  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            {/* Entity Selection */}
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                      {level === 'university' ? <SchoolIcon /> : 
                       level === 'office' ? <BusinessIcon /> : <PersonIcon />}
                    </Avatar>
                    <Typography variant="h6">
                      Select {level === 'university' ? 'Universities' : 
                              level === 'office' ? 'Offices' : 'Users'}
                    </Typography>
                  </Box>
                  
                  <Autocomplete
                    multiple
                    options={[]} // Load from API
                    value={formData.entities}
                    onChange={(_, value) => setFormData(prev => ({ ...prev, entities: value }))}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label={`Select ${level === 'university' ? 'Universities' : 
                                        level === 'office' ? 'Offices' : 'Users'}`}
                        error={!!errors.entities}
                        helperText={errors.entities}
                      />
                    )}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip
                          variant="outlined"
                          label={option.name}
                          {...getTagProps({ index })}
                          key={option.id}
                        />
                      ))
                    }
                  />
                  
                  {formData.entities.length > 0 && (
                    <Alert severity="info" sx={{ mt: 2 }}>
                      {formData.entities.length} {level === 'university' ? 'universities' : 
                                                   level === 'office' ? 'offices' : 'users'} selected
                    </Alert>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* KPI & Academic Year Selection */}
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                      <AssignmentIcon />
                    </Avatar>
                    <Typography variant="h6">KPI & Academic Year</Typography>
                  </Box>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Autocomplete
                        options={[]} // Load from API
                        value={formData.kpi}
                        onChange={(_, value) => setFormData(prev => ({ ...prev, kpi: value }))}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="Select KPI"
                            error={!!errors.kpi}
                            helperText={errors.kpi}
                          />
                        )}
                      />
                    </Grid>
                    
                    <Grid item xs={12}>
                      <FormControl fullWidth error={!!errors.academicYear}>
                        <InputLabel>Academic Year</InputLabel>
                        <Select
                          value={formData.academicYear || ''}
                          onChange={(e) => setFormData(prev => ({ ...prev, academicYear: e.target.value }))}
                        >
                          {/* Load from API */}
                          <MenuItem value="2024-25">2024/25</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        );

      case 1:
        const totalWeight = getTotalWeight();
        const isWeightValid = Math.abs(totalWeight - 100) < 0.01;

        return (
          <Grid container spacing={3}>
            {/* Distribution Mode */}
            <Grid item xs={12}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>Weight Distribution Mode</Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={4}>
                      <Button
                        variant={formData.distributionMode === 'equal' ? 'contained' : 'outlined'}
                        fullWidth
                        onClick={() => {
                          setFormData(prev => ({ ...prev, distributionMode: 'equal' }));
                          const newAssignments = distributeWeights('equal', formData.entities);
                          setFormData(prev => ({ ...prev, assignments: newAssignments }));
                        }}
                        startIcon={<SpeedIcon />}
                      >
                        Equal Distribution
                      </Button>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Button
                        variant={formData.distributionMode === 'capacity' ? 'contained' : 'outlined'}
                        fullWidth
                        onClick={() => {
                          setFormData(prev => ({ ...prev, distributionMode: 'capacity' }));
                          const newAssignments = distributeWeights('capacity', formData.entities);
                          setFormData(prev => ({ ...prev, assignments: newAssignments }));
                        }}
                        startIcon={<TargetIcon />}
                      >
                        Capacity Based
                      </Button>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Button
                        variant={formData.distributionMode === 'manual' ? 'contained' : 'outlined'}
                        fullWidth
                        onClick={() => {
                          setFormData(prev => ({ ...prev, distributionMode: 'manual' }));
                          const newAssignments = distributeWeights('manual', formData.entities);
                          setFormData(prev => ({ ...prev, assignments: newAssignments }));
                        }}
                        startIcon={<AssignmentIcon />}
                      >
                        Manual Entry
                      </Button>
                    </Grid>
                  </Grid>

                  {/* Weight Summary */}
                  <Box sx={{ mt: 3, p: 2, bgcolor: isWeightValid ? 'success.light' : 'warning.light', borderRadius: 1 }}>
                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {isWeightValid ? <CheckCircleIcon color="success" /> : <WarningIcon color="warning" />}
                      Total Weight: {totalWeight.toFixed(2)}%
                      {isWeightValid ? ' ✓ Perfect!' : ` (${totalWeight > 100 ? 'Over' : 'Under'} allocated)`}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Assignment Table */}
            <Grid item xs={12}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>Assignment Configuration</Typography>

                  {formData.assignments.length > 0 ? (
                    <TableContainer>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>Entity</TableCell>
                            <TableCell>Weight (%)</TableCell>
                            <TableCell>Target</TableCell>
                            <TableCell>Baseline</TableCell>
                            <TableCell>Notes</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {formData.assignments.map((assignment, index) => (
                            <TableRow key={assignment.id}>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
                                    {level === 'university' ? <SchoolIcon /> :
                                     level === 'office' ? <BusinessIcon /> : <PersonIcon />}
                                  </Avatar>
                                  {assignment.name}
                                </Box>
                              </TableCell>
                              <TableCell>
                                <TextField
                                  type="number"
                                  value={assignment.weight || ''}
                                  onChange={(e) => updateAssignmentWeight(index, e.target.value)}
                                  size="small"
                                  inputProps={{ min: 0, max: 100, step: 0.01 }}
                                  InputProps={{
                                    endAdornment: <InputAdornment position="end">%</InputAdornment>
                                  }}
                                  sx={{ width: 100 }}
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  value={assignment.target || ''}
                                  onChange={(e) => updateAssignmentField(index, 'target', e.target.value)}
                                  size="small"
                                  placeholder="Target value"
                                  InputProps={{
                                    endAdornment: formData.kpi?.measurement_unit?.symbol && (
                                      <InputAdornment position="end">
                                        {formData.kpi.measurement_unit.symbol}
                                      </InputAdornment>
                                    )
                                  }}
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  value={assignment.baseline || ''}
                                  onChange={(e) => updateAssignmentField(index, 'baseline', e.target.value)}
                                  size="small"
                                  placeholder="Baseline"
                                  InputProps={{
                                    endAdornment: formData.kpi?.measurement_unit?.symbol && (
                                      <InputAdornment position="end">
                                        {formData.kpi.measurement_unit.symbol}
                                      </InputAdornment>
                                    )
                                  }}
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  value={assignment.notes || ''}
                                  onChange={(e) => updateAssignmentField(index, 'notes', e.target.value)}
                                  size="small"
                                  placeholder="Optional notes"
                                  multiline
                                  maxRows={2}
                                />
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  ) : (
                    <Alert severity="info">
                      Please select entities and a KPI in the previous step to configure assignments.
                    </Alert>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Advanced Options */}
            <Grid item xs={12}>
              <Card variant="outlined">
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Typography variant="h6">Advanced Options</Typography>
                    <IconButton onClick={() => setShowAdvanced(!showAdvanced)}>
                      {showAdvanced ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </IconButton>
                  </Box>

                  <Collapse in={showAdvanced}>
                    <Grid container spacing={2} sx={{ mt: 1 }}>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          label="Global Target"
                          value={formData.globalTarget}
                          onChange={(e) => setFormData(prev => ({ ...prev, globalTarget: e.target.value }))}
                          fullWidth
                          size="small"
                          helperText="Apply this target to all assignments"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          label="Global Baseline"
                          value={formData.globalBaseline}
                          onChange={(e) => setFormData(prev => ({ ...prev, globalBaseline: e.target.value }))}
                          fullWidth
                          size="small"
                          helperText="Apply this baseline to all assignments"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          label="Start Date"
                          type="date"
                          value={formData.startDate}
                          onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                          fullWidth
                          size="small"
                          InputLabelProps={{ shrink: true }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          label="Target Date"
                          type="date"
                          value={formData.targetDate}
                          onChange={(e) => setFormData(prev => ({ ...prev, targetDate: e.target.value }))}
                          fullWidth
                          size="small"
                          InputLabelProps={{ shrink: true }}
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          label="Notes"
                          value={formData.notes}
                          onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                          fullWidth
                          multiline
                          rows={3}
                          size="small"
                          helperText="Additional notes for all assignments"
                        />
                      </Grid>
                    </Grid>
                  </Collapse>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        );

      default:
        return null;
    }
  };

  return (
    <Container maxWidth="lg">
      <Paper sx={{ p: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" gutterBottom>
            Create KPI Assignment
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Streamlined process to assign KPIs efficiently
          </Typography>
        </Box>

        {/* Progress Stepper */}
        <Stepper activeStep={activeStep} orientation="vertical">
          {steps.map((step, index) => (
            <Step key={step.label}>
              <StepLabel
                optional={
                  <Typography variant="caption">{step.description}</Typography>
                }
                StepIconComponent={() => (
                  <Avatar
                    sx={{
                      bgcolor: index <= activeStep ? 'primary.main' : 'grey.300',
                      width: 32,
                      height: 32,
                    }}
                  >
                    {step.icon}
                  </Avatar>
                )}
              >
                {step.label}
              </StepLabel>
              <StepContent>
                {renderStepContent(index)}
                
                {/* Navigation Buttons */}
                <Box sx={{ mt: 3, mb: 2 }}>
                  <Button
                    disabled={index === 0}
                    onClick={handleBack}
                    sx={{ mr: 1 }}
                  >
                    Back
                  </Button>
                  <Button
                    variant="contained"
                    onClick={index === steps.length - 1 ? handleSubmit : handleNext}
                    disabled={loading}
                  >
                    {loading ? 'Processing...' : 
                     index === steps.length - 1 ? 'Create Assignments' : 'Next'}
                  </Button>
                  <Button
                    onClick={onCancel}
                    sx={{ ml: 1 }}
                  >
                    Cancel
                  </Button>
                </Box>
              </StepContent>
            </Step>
          ))}
        </Stepper>
      </Paper>
    </Container>
  );
};

export default ImprovedAssignmentWizard;
