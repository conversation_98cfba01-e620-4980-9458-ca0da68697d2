import React, { useState } from 'react';
import {
  Con<PERSON>er,
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  Avatar,
  Chip,
  LinearProgress,
  IconButton,
  Menu,
  MenuItem,
  Divider,
  Alert,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondary,
  Fab,
  Tooltip,
  Badge,
} from '@mui/material';
import {
  Assignment as AssignmentIcon,
  Add as AddIcon,
  School as SchoolIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  MoreVert as MoreVertIcon,
  FilterList as FilterIcon,
  Download as DownloadIcon,
  Upload as UploadIcon,
  AutoAwesome as AutoAwesomeIcon,
  Speed as SpeedIcon,
} from '@mui/icons-material';
import { useTheme, alpha } from '@mui/material/styles';

/**
 * Improved Assignment Dashboard
 * 
 * Key Features:
 * 1. Visual KPI assignment overview
 * 2. Quick action buttons
 * 3. Progress tracking
 * 4. Smart suggestions
 * 5. Bulk operations
 */
const AssignmentDashboardImproved = () => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState(null);

  // Mock data for demonstration
  const stats = {
    total: 92,
    assigned: 78,
    pending: 14,
    overdue: 3,
    completion: 85,
  };

  const recentAssignments = [
    {
      id: 1,
      kpi: 'Student Enrollment Rate',
      entity: 'University of Gondar',
      type: 'university',
      status: 'completed',
      progress: 100,
      dueDate: '2024-12-31',
    },
    {
      id: 2,
      kpi: 'Research Output Quality',
      entity: 'College of Medicine',
      type: 'office',
      status: 'in_progress',
      progress: 65,
      dueDate: '2024-12-31',
    },
    {
      id: 3,
      kpi: 'Student Satisfaction Score',
      entity: 'Dr. Abebe Kebede',
      type: 'user',
      status: 'pending',
      progress: 0,
      dueDate: '2024-12-31',
    },
  ];

  const quickActions = [
    {
      title: 'Smart Assignment',
      description: 'AI-powered KPI assignment with recommendations',
      icon: <AutoAwesomeIcon />,
      color: 'primary',
      action: () => console.log('Smart assignment'),
    },
    {
      title: 'Bulk Assignment',
      description: 'Assign multiple KPIs to multiple entities',
      icon: <AssignmentIcon />,
      color: 'secondary',
      action: () => console.log('Bulk assignment'),
    },
    {
      title: 'Import from Template',
      description: 'Use predefined assignment templates',
      icon: <UploadIcon />,
      color: 'success',
      action: () => console.log('Import template'),
    },
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'in_progress': return 'warning';
      case 'pending': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return <CheckCircleIcon />;
      case 'in_progress': return <ScheduleIcon />;
      case 'pending': return <WarningIcon />;
      default: return <AssignmentIcon />;
    }
  };

  const getEntityIcon = (type) => {
    switch (type) {
      case 'university': return <SchoolIcon />;
      case 'office': return <BusinessIcon />;
      case 'user': return <PersonIcon />;
      default: return <AssignmentIcon />;
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          KPI Assignment Dashboard
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Streamlined KPI assignment management for Ethiopian universities
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ 
            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.primary.main, 0.05)})`,
            border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
          }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: theme.palette.primary.main, mx: 'auto', mb: 2, width: 56, height: 56 }}>
                <AssignmentIcon sx={{ fontSize: 28 }} />
              </Avatar>
              <Typography variant="h4" sx={{ fontWeight: 700, color: theme.palette.primary.main, mb: 1 }}>
                {stats.total}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total KPIs
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ 
            background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.1)}, ${alpha(theme.palette.success.main, 0.05)})`,
            border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`,
          }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: theme.palette.success.main, mx: 'auto', mb: 2, width: 56, height: 56 }}>
                <CheckCircleIcon sx={{ fontSize: 28 }} />
              </Avatar>
              <Typography variant="h4" sx={{ fontWeight: 700, color: theme.palette.success.main, mb: 1 }}>
                {stats.assigned}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Assigned
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ 
            background: `linear-gradient(135deg, ${alpha(theme.palette.warning.main, 0.1)}, ${alpha(theme.palette.warning.main, 0.05)})`,
            border: `1px solid ${alpha(theme.palette.warning.main, 0.2)}`,
          }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: theme.palette.warning.main, mx: 'auto', mb: 2, width: 56, height: 56 }}>
                <ScheduleIcon sx={{ fontSize: 28 }} />
              </Avatar>
              <Typography variant="h4" sx={{ fontWeight: 700, color: theme.palette.warning.main, mb: 1 }}>
                {stats.pending}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Pending
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ 
            background: `linear-gradient(135deg, ${alpha(theme.palette.error.main, 0.1)}, ${alpha(theme.palette.error.main, 0.05)})`,
            border: `1px solid ${alpha(theme.palette.error.main, 0.2)}`,
          }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: theme.palette.error.main, mx: 'auto', mb: 2, width: 56, height: 56 }}>
                <WarningIcon sx={{ fontSize: 28 }} />
              </Avatar>
              <Typography variant="h4" sx={{ fontWeight: 700, color: theme.palette.error.main, mb: 1 }}>
                {stats.overdue}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Overdue
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Progress Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
                <Typography variant="h6">Assignment Progress</Typography>
                <Chip 
                  label={`${stats.completion}% Complete`}
                  color={stats.completion >= 80 ? 'success' : stats.completion >= 60 ? 'warning' : 'error'}
                  sx={{ fontWeight: 600 }}
                />
              </Box>
              
              <LinearProgress 
                variant="determinate" 
                value={stats.completion} 
                sx={{ 
                  height: 12, 
                  borderRadius: 6,
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  '& .MuiLinearProgress-bar': {
                    borderRadius: 6,
                    background: stats.completion >= 80 
                      ? `linear-gradient(90deg, ${theme.palette.success.main}, ${theme.palette.success.light})`
                      : stats.completion >= 60 
                      ? `linear-gradient(90deg, ${theme.palette.warning.main}, ${theme.palette.warning.light})`
                      : `linear-gradient(90deg, ${theme.palette.error.main}, ${theme.palette.error.light})`
                  }
                }} 
              />
              
              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                {stats.assigned} of {stats.total} KPIs have been assigned to entities
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Quick Actions</Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                {quickActions.map((action, index) => (
                  <Button
                    key={index}
                    variant="outlined"
                    startIcon={action.icon}
                    onClick={action.action}
                    sx={{ justifyContent: 'flex-start', textAlign: 'left' }}
                  >
                    <Box>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        {action.title}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {action.description}
                      </Typography>
                    </Box>
                  </Button>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recent Assignments */}
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
                <Typography variant="h6">Recent Assignments</Typography>
                <Box>
                  <IconButton>
                    <FilterIcon />
                  </IconButton>
                  <IconButton>
                    <DownloadIcon />
                  </IconButton>
                  <IconButton onClick={(e) => setAnchorEl(e.currentTarget)}>
                    <MoreVertIcon />
                  </IconButton>
                </Box>
              </Box>

              <List>
                {recentAssignments.map((assignment, index) => (
                  <React.Fragment key={assignment.id}>
                    <ListItem>
                      <ListItemIcon>
                        <Avatar sx={{ bgcolor: `${getStatusColor(assignment.status)}.main` }}>
                          {getEntityIcon(assignment.type)}
                        </Avatar>
                      </ListItemIcon>
                      <ListItemText
                        primary={assignment.kpi}
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              {assignment.entity}
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                              <LinearProgress 
                                variant="determinate" 
                                value={assignment.progress} 
                                sx={{ flexGrow: 1, mr: 2, height: 6, borderRadius: 3 }}
                              />
                              <Typography variant="caption">
                                {assignment.progress}%
                              </Typography>
                            </Box>
                          </Box>
                        }
                      />
                      <Box sx={{ textAlign: 'right' }}>
                        <Chip
                          icon={getStatusIcon(assignment.status)}
                          label={assignment.status.replace('_', ' ')}
                          color={getStatusColor(assignment.status)}
                          size="small"
                          sx={{ mb: 1 }}
                        />
                        <Typography variant="caption" display="block" color="text.secondary">
                          Due: {assignment.dueDate}
                        </Typography>
                      </Box>
                    </ListItem>
                    {index < recentAssignments.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Floating Action Button */}
      <Tooltip title="Create New Assignment">
        <Fab
          color="primary"
          sx={{
            position: 'fixed',
            bottom: 24,
            right: 24,
            background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.primary.light})`,
          }}
        >
          <AddIcon />
        </Fab>
      </Tooltip>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={() => setAnchorEl(null)}
      >
        <MenuItem onClick={() => setAnchorEl(null)}>
          <ListItemIcon><DownloadIcon /></ListItemIcon>
          Export All
        </MenuItem>
        <MenuItem onClick={() => setAnchorEl(null)}>
          <ListItemIcon><UploadIcon /></ListItemIcon>
          Import Template
        </MenuItem>
        <MenuItem onClick={() => setAnchorEl(null)}>
          <ListItemIcon><SpeedIcon /></ListItemIcon>
          Bulk Operations
        </MenuItem>
      </Menu>
    </Container>
  );
};

export default AssignmentDashboardImproved;
