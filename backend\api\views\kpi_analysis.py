"""
Views for KPI analysis and dashboard data.
"""

from rest_framework import viewsets, permissions, status
from rest_framework.response import Response
from rest_framework.decorators import action
from django.db import connection, models
from django_tenants.utils import get_public_schema_name
from django.db.models import Avg, Count, Sum, F, Q, Case, When, Value, FloatField, ExpressionWrapper
from django.utils import timezone
import sys
import os

# Add the parent directory to sys.path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import directly from models.py file
try:
    from kpi_assignments.models import (
        UniversityKPIAssignment, OfficeKPIAssignment, UserKPIAssignment,
        KPIReport, OfficeKPIReport, UserKPIReport
    )
except ImportError:
    # Define placeholder classes for type checking
    class UniversityKPIAssignment:
        class ObjectManager:
            def all(self):
                return []

            def filter(self, **kwargs):
                return []

            def get(self, **kwargs):
                return None

            def values(self, *args):
                return self

            def annotate(self, **kwargs):
                return self

            def order_by(self, *args):
                return self

        objects = ObjectManager()

    class KPIReport:
        class ObjectManager:
            def all(self):
                return []

            def filter(self, **kwargs):
                return []

            def get(self, **kwargs):
                return None

            def values(self, *args):
                return self

            def annotate(self, **kwargs):
                return self

            def order_by(self, *args):
                return self

        objects = ObjectManager()

try:
    from kpi_definitions.models import KPI, AcademicYear, Theme, SubTheme
except ImportError:
    # Define placeholder classes for type checking
    class KPI:
        class ObjectManager:
            def all(self):
                return []

            def filter(self, **kwargs):
                return []

            def get(self, **kwargs):
                return None

        objects = ObjectManager()

    class AcademicYear:
        class ObjectManager:
            def all(self):
                return []

            def filter(self, **kwargs):
                return []

            def get(self, **kwargs):
                return None

        objects = ObjectManager()

    class Theme:
        class ObjectManager:
            def all(self):
                return []

            def filter(self, **kwargs):
                return []

            def get(self, **kwargs):
                return None

        objects = ObjectManager()

    class SubTheme:
        class ObjectManager:
            def all(self):
                return []

            def filter(self, **kwargs):
                return []

            def get(self, **kwargs):
                return None

        objects = ObjectManager()

try:
    from universities.models import University
except ImportError:
    # Define placeholder class for type checking
    class University:
        class ObjectManager:
            def all(self):
                return []

            def filter(self, **kwargs):
                return []

            def get(self, **kwargs):
                return None

        objects = ObjectManager()

from api.permissions import IsMoEStaffOrReadOnly
from rest_framework.viewsets import ViewSet


class KPIAnalysisViewSet(ViewSet):
    """
    ViewSet for KPI analysis and dashboard data.
    """
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=['get'])
    def university_performance(self, request):
        """
        Get performance data for all universities.

        Query parameters:
        - academic_year: ID of the academic year
        - theme: ID of the theme (optional)
        """
        academic_year_id = request.query_params.get('academic_year')
        theme_id = request.query_params.get('theme')

        if not academic_year_id:
            return Response({"error": "Academic year is required"}, status=status.HTTP_400_BAD_REQUEST)

        # Check if we're in the public schema
        is_public = connection.schema_name == get_public_schema_name()

        if is_public:
            # In public schema, get data for all universities
            query = UniversityKPIAssignment.objects.filter(
                academic_year_id=academic_year_id,
                is_active=True
            )

            if theme_id:
                query = query.filter(kpi__theme_id=theme_id)

            # Calculate average progress for each university
            university_data = query.values('university__name').annotate(
                avg_progress=Avg(
                    ExpressionWrapper(
                        F('current_value') * 100.0 / F('target'),
                        output_field=FloatField()
                    )
                )
            ).order_by('-avg_progress')

            # Format the response
            result = [
                {
                    "name": item['university__name'],
                    "progress": round(item['avg_progress'], 2) if item['avg_progress'] else 0
                }
                for item in university_data
            ]

            return Response(result)
        else:
            # In university schema, get data for the current university only
            university = University.objects.get(schema_name=connection.schema_name)

            query = UniversityKPIAssignment.objects.filter(
                university=university,
                academic_year_id=academic_year_id,
                is_active=True
            )

            if theme_id:
                query = query.filter(kpi__theme_id=theme_id)

            # Calculate average progress
            avg_progress = query.aggregate(
                avg_progress=Avg(
                    ExpressionWrapper(
                        F('current_value') * 100.0 / F('target'),
                        output_field=FloatField()
                    )
                )
            )['avg_progress'] or 0

            result = [
                {
                    "name": university.name,
                    "progress": round(avg_progress, 2)
                }
            ]

            return Response(result)

    @action(detail=False, methods=['get'])
    def theme_performance(self, request):
        """
        Get performance data by theme.

        Query parameters:
        - academic_year: ID of the academic year
        """
        academic_year_id = request.query_params.get('academic_year')

        if not academic_year_id:
            return Response({"error": "Academic year is required"}, status=status.HTTP_400_BAD_REQUEST)

        # Check if we're in the public schema
        is_public = connection.schema_name == get_public_schema_name()

        if is_public:
            # In public schema, get data across all universities
            query = UniversityKPIAssignment.objects.filter(
                academic_year_id=academic_year_id,
                is_active=True
            )

            # Calculate average progress for each theme
            theme_data = query.values('kpi__theme__name').annotate(
                avg_progress=Avg(
                    ExpressionWrapper(
                        F('current_value') * 100.0 / F('target'),
                        output_field=FloatField()
                    )
                )
            ).order_by('-avg_progress')

            # Format the response
            result = [
                {
                    "name": item['kpi__theme__name'] or "Uncategorized",
                    "progress": round(item['avg_progress'], 2) if item['avg_progress'] else 0
                }
                for item in theme_data
            ]

            return Response(result)
        else:
            # In university schema, get data for the current university only
            university = University.objects.get(schema_name=connection.schema_name)

            query = UniversityKPIAssignment.objects.filter(
                university=university,
                academic_year_id=academic_year_id,
                is_active=True
            )

            # Calculate average progress for each theme
            # Since current_value is a property, we need to calculate it differently
            theme_data = []
            themes = query.values('kpi__theme__name').distinct()

            for theme in themes:
                theme_assignments = query.filter(kpi__theme__name=theme['kpi__theme__name'])
                total_progress = 0
                count = 0

                for assignment in theme_assignments:
                    # Get the assignment object to access the current_value property
                    assignment_obj = UniversityKPIAssignment.objects.get(id=assignment['id'])
                    current_val = assignment_obj.current_value
                    target_val = assignment_obj.target

                    if current_val is not None and target_val and target_val > 0:
                        progress = (float(current_val) / float(target_val)) * 100
                        total_progress += progress
                        count += 1

                avg_progress = total_progress / count if count > 0 else 0
                theme_data.append({
                    'kpi__theme__name': theme['kpi__theme__name'],
                    'avg_progress': avg_progress
                })

            # Sort by average progress
            theme_data = sorted(theme_data, key=lambda x: x['avg_progress'], reverse=True)

            # Format the response
            result = [
                {
                    "name": item['kpi__theme__name'] or "Uncategorized",
                    "progress": round(item['avg_progress'], 2) if item['avg_progress'] else 0
                }
                for item in theme_data
            ]

            return Response(result)

    @action(detail=False, methods=['get'])
    def performance_trend(self, request):
        """
        Get performance trend data over time.

        Query parameters:
        - academic_year: ID of the academic year
        - theme: ID of the theme (optional)
        """
        academic_year_id = request.query_params.get('academic_year')
        theme_id = request.query_params.get('theme')

        if not academic_year_id:
            return Response({"error": "Academic year is required"}, status=status.HTTP_400_BAD_REQUEST)

        # Check if we're in the public schema
        is_public = connection.schema_name == get_public_schema_name()

        if is_public:
            # In public schema, get trend data across all universities
            query = KPIReport.objects.filter(
                university_assignment__academic_year_id=academic_year_id,
                status='approved'
            )

            if theme_id:
                query = query.filter(university_assignment__kpi__theme_id=theme_id)

            # Calculate average progress for each reporting period
            trend_data = query.values('reporting_period').annotate(
                avg_progress=Avg('progress')
            ).order_by('reporting_period')

            # Format the response
            result = [
                {
                    "period": item['reporting_period'],
                    "progress": round(item['avg_progress'], 2) if item['avg_progress'] else 0
                }
                for item in trend_data
            ]

            return Response(result)
        else:
            # In university schema, get trend data for the current university only
            university = University.objects.get(schema_name=connection.schema_name)

            query = KPIReport.objects.filter(
                university_assignment__university=university,
                university_assignment__academic_year_id=academic_year_id,
                status='approved'
            )

            if theme_id:
                query = query.filter(university_assignment__kpi__theme_id=theme_id)

            # Calculate average progress for each reporting period
            trend_data = query.values('reporting_period').annotate(
                avg_progress=Avg('progress')
            ).order_by('reporting_period')

            # Format the response
            result = [
                {
                    "period": item['reporting_period'],
                    "progress": round(item['avg_progress'], 2) if item['avg_progress'] else 0
                }
                for item in trend_data
            ]

            return Response(result)

    @action(detail=False, methods=['get'])
    def top_performers(self, request):
        """
        Get top performing entities (universities, offices, or users).

        Query parameters:
        - academic_year: ID of the academic year
        - theme: ID of the theme (optional)
        - limit: Number of results to return (default: 5)
        - level: Level of analysis (university, office, user) (default: university)
        """
        academic_year_id = request.query_params.get('academic_year')
        theme_id = request.query_params.get('theme')
        limit = int(request.query_params.get('limit', 5))
        level = request.query_params.get('level', 'university')

        if not academic_year_id:
            return Response({"error": "Academic year is required"}, status=status.HTTP_400_BAD_REQUEST)

        # Check if we're in the public schema
        is_public = connection.schema_name == get_public_schema_name()

        if level == 'university' and is_public:
            # In public schema, get top performing universities
            query = UniversityKPIAssignment.objects.filter(
                academic_year_id=academic_year_id,
                is_active=True
            )

            if theme_id:
                query = query.filter(kpi__theme_id=theme_id)

            # Calculate average progress for each university
            # Since current_value is a property, we need to calculate it differently
            university_data = []
            universities = query.values('university_id', 'university__name').distinct()

            for univ in universities:
                univ_assignments = query.filter(university_id=univ['university_id'])
                total_progress = 0
                count = 0

                for assignment in univ_assignments:
                    # Get the assignment object to access the current_value property
                    assignment_obj = UniversityKPIAssignment.objects.get(id=assignment['id'])
                    current_val = assignment_obj.current_value
                    target_val = assignment_obj.target

                    if current_val is not None and target_val and target_val > 0:
                        progress = (float(current_val) / float(target_val)) * 100
                        total_progress += progress
                        count += 1

                avg_progress = total_progress / count if count > 0 else 0
                university_data.append({
                    'university_id': univ['university_id'],
                    'university__name': univ['university__name'],
                    'avg_progress': avg_progress
                })

            # Sort by average progress
            university_data = sorted(university_data, key=lambda x: x['avg_progress'], reverse=True)[:limit]

            # Format the response
            result = [
                {
                    "id": item['university_id'],
                    "name": item['university__name'],
                    "progress": round(item['avg_progress'], 2) if item['avg_progress'] else 0
                }
                for item in university_data
            ]

            return Response(result)
        elif level == 'office':
            # Get top performing offices
            university = None
            if not is_public:
                university = University.objects.get(schema_name=connection.schema_name)

            query = OfficeKPIAssignment.objects.filter(
                university_assignment__academic_year_id=academic_year_id,
                is_active=True
            )

            if university:
                query = query.filter(university_assignment__university=university)

            if theme_id:
                query = query.filter(university_assignment__kpi__theme_id=theme_id)

            # Calculate average progress for each office
            office_data = query.values('office_id', 'office__name').annotate(
                avg_progress=Avg(
                    ExpressionWrapper(
                        F('current_value') * 100.0 / F('target'),
                        output_field=FloatField()
                    )
                )
            ).order_by('-avg_progress')[:limit]

            # Format the response
            result = [
                {
                    "id": item['office_id'],
                    "name": item['office__name'],
                    "progress": round(item['avg_progress'], 2) if item['avg_progress'] else 0
                }
                for item in office_data
            ]

            return Response(result)
        elif level == 'user':
            # Get top performing users
            university = None
            if not is_public:
                university = University.objects.get(schema_name=connection.schema_name)

            query = UserKPIAssignment.objects.filter(
                office_assignment__university_assignment__academic_year_id=academic_year_id,
                is_active=True
            )

            if university:
                query = query.filter(office_assignment__university_assignment__university=university)

            if theme_id:
                query = query.filter(office_assignment__university_assignment__kpi__theme_id=theme_id)

            # Calculate average progress for each user
            user_data = query.values('user_id', 'user__first_name', 'user__last_name').annotate(
                avg_progress=Avg(
                    ExpressionWrapper(
                        F('current_value') * 100.0 / F('target'),
                        output_field=FloatField()
                    )
                )
            ).order_by('-avg_progress')[:limit]

            # Format the response
            result = [
                {
                    "id": item['user_id'],
                    "name": f"{item['user__first_name']} {item['user__last_name']}",
                    "progress": round(item['avg_progress'], 2) if item['avg_progress'] else 0
                }
                for item in user_data
            ]

            return Response(result)
        else:
            # In university schema, this doesn't make sense for university level, so return empty list
            return Response([])

    @action(detail=False, methods=['get'])
    def low_performers(self, request):
        """
        Get low performing entities (universities, offices, or users).

        Query parameters:
        - academic_year: ID of the academic year
        - theme: ID of the theme (optional)
        - limit: Number of results to return (default: 5)
        - level: Level of analysis (university, office, user) (default: university)
        """
        academic_year_id = request.query_params.get('academic_year')
        theme_id = request.query_params.get('theme')
        limit = int(request.query_params.get('limit', 5))
        level = request.query_params.get('level', 'university')

        if not academic_year_id:
            return Response({"error": "Academic year is required"}, status=status.HTTP_400_BAD_REQUEST)

        # Check if we're in the public schema
        is_public = connection.schema_name == get_public_schema_name()

        if level == 'university' and is_public:
            # In public schema, get low performing universities
            query = UniversityKPIAssignment.objects.filter(
                academic_year_id=academic_year_id,
                is_active=True
            )

            if theme_id:
                query = query.filter(kpi__theme_id=theme_id)

            # Calculate average progress for each university
            # Since current_value is a property, we need to calculate it differently
            university_data = []
            universities = query.values('university_id', 'university__name').distinct()

            for univ in universities:
                univ_assignments = query.filter(university_id=univ['university_id'])
                total_progress = 0
                count = 0

                for assignment in univ_assignments:
                    # Get the assignment object to access the current_value property
                    assignment_obj = UniversityKPIAssignment.objects.get(id=assignment['id'])
                    current_val = assignment_obj.current_value
                    target_val = assignment_obj.target

                    if current_val is not None and target_val and target_val > 0:
                        progress = (float(current_val) / float(target_val)) * 100
                        total_progress += progress
                        count += 1

                avg_progress = total_progress / count if count > 0 else 0
                university_data.append({
                    'university_id': univ['university_id'],
                    'university__name': univ['university__name'],
                    'avg_progress': avg_progress
                })

            # Sort by average progress (ascending for low performers)
            university_data = sorted(university_data, key=lambda x: x['avg_progress'])[:limit]

            # Format the response
            result = [
                {
                    "id": item['university_id'],
                    "name": item['university__name'],
                    "progress": round(item['avg_progress'], 2) if item['avg_progress'] else 0
                }
                for item in university_data
            ]

            return Response(result)
        elif level == 'office':
            # Get low performing offices
            university = None
            if not is_public:
                university = University.objects.get(schema_name=connection.schema_name)

            query = OfficeKPIAssignment.objects.filter(
                university_assignment__academic_year_id=academic_year_id,
                is_active=True
            )

            if university:
                query = query.filter(university_assignment__university=university)

            if theme_id:
                query = query.filter(university_assignment__kpi__theme_id=theme_id)

            # Calculate average progress for each office
            office_data = query.values('office_id', 'office__name').annotate(
                avg_progress=Avg(
                    ExpressionWrapper(
                        F('current_value') * 100.0 / F('target'),
                        output_field=FloatField()
                    )
                )
            ).order_by('avg_progress')[:limit]

            # Format the response
            result = [
                {
                    "id": item['office_id'],
                    "name": item['office__name'],
                    "progress": round(item['avg_progress'], 2) if item['avg_progress'] else 0
                }
                for item in office_data
            ]

            return Response(result)
        elif level == 'user':
            # Get low performing users
            university = None
            if not is_public:
                university = University.objects.get(schema_name=connection.schema_name)

            query = UserKPIAssignment.objects.filter(
                office_assignment__university_assignment__academic_year_id=academic_year_id,
                is_active=True
            )

            if university:
                query = query.filter(office_assignment__university_assignment__university=university)

            if theme_id:
                query = query.filter(office_assignment__university_assignment__kpi__theme_id=theme_id)

            # Calculate average progress for each user
            user_data = query.values('user_id', 'user__first_name', 'user__last_name').annotate(
                avg_progress=Avg(
                    ExpressionWrapper(
                        F('current_value') * 100.0 / F('target'),
                        output_field=FloatField()
                    )
                )
            ).order_by('avg_progress')[:limit]

            # Format the response
            result = [
                {
                    "id": item['user_id'],
                    "name": f"{item['user__first_name']} {item['user__last_name']}",
                    "progress": round(item['avg_progress'], 2) if item['avg_progress'] else 0
                }
                for item in user_data
            ]

            return Response(result)
        else:
            # In university schema, this doesn't make sense for university level, so return empty list
            return Response([])

    @action(detail=False, methods=['get'])
    def office_performance(self, request):
        """
        Get performance data for offices.

        Query parameters:
        - academic_year: ID of the academic year
        - theme: ID of the theme (optional)
        - university: ID of the university (only in public schema)
        """
        academic_year_id = request.query_params.get('academic_year')
        theme_id = request.query_params.get('theme')
        university_id = request.query_params.get('university')

        if not academic_year_id:
            return Response({"error": "Academic year is required"}, status=status.HTTP_400_BAD_REQUEST)

        # Check if we're in the public schema
        is_public = connection.schema_name == get_public_schema_name()

        if is_public and not university_id:
            return Response({"error": "University ID is required in public schema"}, status=status.HTTP_400_BAD_REQUEST)

        # Get the university
        university = None
        if is_public:
            try:
                university = University.objects.get(id=university_id)
            except University.DoesNotExist:
                return Response({"error": "University not found"}, status=status.HTTP_404_NOT_FOUND)
        else:
            university = University.objects.get(schema_name=connection.schema_name)

        # Get office performance data
        query = OfficeKPIAssignment.objects.filter(
            university_assignment__university=university,
            university_assignment__academic_year_id=academic_year_id,
            is_active=True
        )

        if theme_id:
            query = query.filter(university_assignment__kpi__theme_id=theme_id)

        # Calculate average progress for each office
        office_data = query.values('office_id', 'office__name').annotate(
            avg_progress=Avg(
                ExpressionWrapper(
                    F('current_value') * 100.0 / F('target'),
                    output_field=FloatField()
                )
            )
        ).order_by('-avg_progress')

        # Format the response
        result = [
            {
                "id": item['office_id'],
                "name": item['office__name'],
                "progress": round(item['avg_progress'], 2) if item['avg_progress'] else 0
            }
            for item in office_data
        ]

        return Response(result)

    @action(detail=False, methods=['get'])
    def user_performance(self, request):
        """
        Get performance data for users.

        Query parameters:
        - academic_year: ID of the academic year
        - theme: ID of the theme (optional)
        - office: ID of the office (required)
        - university: ID of the university (only in public schema)
        """
        academic_year_id = request.query_params.get('academic_year')
        theme_id = request.query_params.get('theme')
        office_id = request.query_params.get('office')
        university_id = request.query_params.get('university')

        if not academic_year_id:
            return Response({"error": "Academic year is required"}, status=status.HTTP_400_BAD_REQUEST)

        if not office_id:
            return Response({"error": "Office ID is required"}, status=status.HTTP_400_BAD_REQUEST)

        # Check if we're in the public schema
        is_public = connection.schema_name == get_public_schema_name()

        if is_public and not university_id:
            return Response({"error": "University ID is required in public schema"}, status=status.HTTP_400_BAD_REQUEST)

        # Get the university
        university = None
        if is_public:
            try:
                university = University.objects.get(id=university_id)
            except University.DoesNotExist:
                return Response({"error": "University not found"}, status=status.HTTP_404_NOT_FOUND)
        else:
            university = University.objects.get(schema_name=connection.schema_name)

        # Get user performance data
        query = UserKPIAssignment.objects.filter(
            office_assignment__office_id=office_id,
            office_assignment__university_assignment__university=university,
            office_assignment__university_assignment__academic_year_id=academic_year_id,
            is_active=True
        )

        if theme_id:
            query = query.filter(office_assignment__university_assignment__kpi__theme_id=theme_id)

        # Calculate average progress for each user
        user_data = query.values('user_id', 'user__first_name', 'user__last_name').annotate(
            avg_progress=Avg(
                ExpressionWrapper(
                    F('current_value') * 100.0 / F('target'),
                    output_field=FloatField()
                )
            )
        ).order_by('-avg_progress')

        # Format the response
        result = [
            {
                "id": item['user_id'],
                "name": f"{item['user__first_name']} {item['user__last_name']}",
                "progress": round(item['avg_progress'], 2) if item['avg_progress'] else 0
            }
            for item in user_data
        ]

        return Response(result)

    @action(detail=False, methods=['get'])
    def kpi_distribution(self, request):
        """
        Get KPI status distribution.

        Query parameters:
        - academic_year: ID of the academic year
        """
        print("KPI distribution endpoint called")
        academic_year_id = request.query_params.get('academic_year')
        print(f"Academic year ID: {academic_year_id}")

        if not academic_year_id:
            print("Academic year is required")
            return Response({"error": "Academic year is required"}, status=status.HTTP_400_BAD_REQUEST)

        # Check if we're in the public schema
        is_public = connection.schema_name == get_public_schema_name()
        print(f"Is public schema: {is_public}")

        if is_public:
            # In public schema, get KPI distribution across all universities
            print("Getting KPI distribution for public schema")
            try:
                query = UniversityKPIAssignment.objects.filter(
                    academic_year_id=academic_year_id,
                    is_active=True
                )
                print(f"Found {query.count()} university KPI assignments")
            except Exception as e:
                print(f"Error querying university KPI assignments: {str(e)}")
                return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Get all assignments and calculate progress for each
            try:
                print("Converting query to list")
                assignments = list(query)
                print(f"Got {len(assignments)} assignments")

                # Count assignments in each category
                status_counts = {
                    'Excellent': 0,
                    'Good': 0,
                    'Average': 0,
                    'Below Average': 0,
                    'Poor': 0
                }

                print("Calculating progress for each assignment")
                for i, assignment in enumerate(assignments):
                    try:
                        print(f"Processing assignment {i+1}/{len(assignments)}")
                        progress = assignment.calculate_progress()
                        print(f"Progress: {progress}")
                        if progress >= 90:
                            status_counts['Excellent'] += 1
                        elif progress >= 75:
                            status_counts['Good'] += 1
                        elif progress >= 50:
                            status_counts['Average'] += 1
                        elif progress >= 25:
                            status_counts['Below Average'] += 1
                        else:
                            status_counts['Poor'] += 1
                    except Exception as e:
                        print(f"Error calculating progress for assignment {i+1}: {str(e)}")
                print(f"Final status counts: {status_counts}")
            except Exception as e:
                print(f"Error processing assignments: {str(e)}")
                return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Convert to the format expected by the frontend
            status_data = [
                {'status_category': category, 'count': count}
                for category, count in status_counts.items()
            ]

            # Format the response
            result = [
                {
                    "name": item['status_category'],
                    "value": item['count']
                }
                for item in status_data
            ]

            return Response(result)
        else:
            # In university schema, get KPI distribution for the current university only
            university = University.objects.get(schema_name=connection.schema_name)

            query = UniversityKPIAssignment.objects.filter(
                university=university,
                academic_year_id=academic_year_id,
                is_active=True
            )

            # Get all assignments and calculate progress for each
            assignments = list(query)

            # Count assignments in each category
            status_counts = {
                'Excellent': 0,
                'Good': 0,
                'Average': 0,
                'Below Average': 0,
                'Poor': 0
            }

            for assignment in assignments:
                progress = assignment.calculate_progress()
                if progress >= 90:
                    status_counts['Excellent'] += 1
                elif progress >= 75:
                    status_counts['Good'] += 1
                elif progress >= 50:
                    status_counts['Average'] += 1
                elif progress >= 25:
                    status_counts['Below Average'] += 1
                else:
                    status_counts['Poor'] += 1

            # Convert to the format expected by the frontend
            status_data = [
                {'status_category': category, 'count': count}
                for category, count in status_counts.items()
            ]

            # Format the response
            result = [
                {
                    "name": item['status_category'],
                    "value": item['count']
                }
                for item in status_data
            ]

            return Response(result)

    @action(detail=False, methods=['get'])
    def kpi_comparison(self, request):
        """
        Compare KPI performance across different levels.

        Query parameters:
        - academic_year: ID of the academic year
        - kpi: ID of the KPI to compare
        - university: ID of the university (only in public schema)
        """
        academic_year_id = request.query_params.get('academic_year')
        kpi_id = request.query_params.get('kpi')
        university_id = request.query_params.get('university')

        if not academic_year_id:
            return Response({"error": "Academic year is required"}, status=status.HTTP_400_BAD_REQUEST)

        if not kpi_id:
            return Response({"error": "KPI ID is required"}, status=status.HTTP_400_BAD_REQUEST)

        # Check if we're in the public schema
        is_public = connection.schema_name == get_public_schema_name()

        if is_public and not university_id:
            # In public schema without university ID, compare across universities
            university_assignments = UniversityKPIAssignment.objects.filter(
                academic_year_id=academic_year_id,
                kpi_id=kpi_id,
                is_active=True
            )

            # Calculate progress for each university
            university_data = university_assignments.values('university_id', 'university__name').annotate(
                progress=ExpressionWrapper(
                    F('current_value') * 100.0 / F('target'),
                    output_field=FloatField()
                )
            ).order_by('-progress')

            # Format the response
            result = {
                "level": "university",
                "kpi_id": kpi_id,
                "data": [
                    {
                        "id": item['university_id'],
                        "name": item['university__name'],
                        "progress": round(item['progress'], 2) if item['progress'] else 0
                    }
                    for item in university_data
                ]
            }

            return Response(result)
        else:
            # Get the university
            university = None
            if is_public:
                try:
                    university = University.objects.get(id=university_id)
                except University.DoesNotExist:
                    return Response({"error": "University not found"}, status=status.HTTP_404_NOT_FOUND)
            else:
                university = University.objects.get(schema_name=connection.schema_name)

            # Get university assignment
            try:
                university_assignment = UniversityKPIAssignment.objects.get(
                    university=university,
                    academic_year_id=academic_year_id,
                    kpi_id=kpi_id,
                    is_active=True
                )
            except UniversityKPIAssignment.DoesNotExist:
                return Response({"error": "KPI assignment not found"}, status=status.HTTP_404_NOT_FOUND)

            # Get office assignments
            office_assignments = OfficeKPIAssignment.objects.filter(
                university_assignment=university_assignment,
                is_active=True
            )

            # Calculate progress for each office
            office_data = office_assignments.values('office_id', 'office__name').annotate(
                progress=ExpressionWrapper(
                    F('current_value') * 100.0 / F('target'),
                    output_field=FloatField()
                )
            ).order_by('-progress')

            # Get user assignments
            user_assignments = UserKPIAssignment.objects.filter(
                office_assignment__university_assignment=university_assignment,
                is_active=True
            )

            # Calculate progress for each user
            user_data = user_assignments.values(
                'user_id', 'user__first_name', 'user__last_name', 'office_assignment__office__name'
            ).annotate(
                progress=ExpressionWrapper(
                    F('current_value') * 100.0 / F('target'),
                    output_field=FloatField()
                )
            ).order_by('-progress')

            # Format the response
            result = {
                "university": {
                    "id": university.id,
                    "name": university.name,
                    "progress": round(
                        university_assignment.current_value * 100.0 / university_assignment.target, 2
                    ) if university_assignment.target else 0
                },
                "offices": [
                    {
                        "id": item['office_id'],
                        "name": item['office__name'],
                        "progress": round(item['progress'], 2) if item['progress'] else 0
                    }
                    for item in office_data
                ],
                "users": [
                    {
                        "id": item['user_id'],
                        "name": f"{item['user__first_name']} {item['user__last_name']}",
                        "office": item['office_assignment__office__name'],
                        "progress": round(item['progress'], 2) if item['progress'] else 0
                    }
                    for item in user_data
                ]
            }

            return Response(result)