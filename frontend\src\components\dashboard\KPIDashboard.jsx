import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Box,
  Paper,
  Divider,
  alpha,
  useTheme,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Avatar,
  LinearProgress,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Button,
  IconButton,
  Tooltip,
  Menu,
  Badge,
  Stack,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  School as SchoolIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
  FilterList as FilterListIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Timeline as TimelineIcon,
  PieChart as PieChartIcon,
  <PERSON><PERSON>hart as BarChartIcon,
  ShowChart as ShowChartIcon,
  Info as InfoIcon,
  Star as StarIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
// import { motion } from 'framer-motion';
import { getDashboardData } from '../../services/kpiAnalysisService';
import { getAcademicYears } from '../../services/academicYearService';
import { getThemes } from '../../services/themeService';
import { getUniversities } from '../../services/universityService';
import { getOffices } from '../../services/officeService';
import PerformanceTrendChart from './charts/PerformanceTrendChart.jsx';
import KPIDistributionChart from './charts/KPIDistributionChart.jsx';
import PerformanceComparisonChart from './charts/PerformanceComparisonChart.jsx';
import TopPerformersChart from './charts/TopPerformersChart.jsx';
import LowPerformersChart from './charts/LowPerformersChart.jsx';
import { useTenant } from '../../contexts/TenantContext';

const KPIDashboard = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { tenant, isPublicTenant } = useTenant();
  const theme = useTheme();

  // Get query parameters
  const queryParams = new URLSearchParams(location.search);
  const levelParam = queryParams.get('level') || 'university';
  const academicYearParam = queryParams.get('academic_year');
  const themeParam = queryParams.get('theme');
  const universityParam = queryParams.get('university');
  const officeParam = queryParams.get('office');

  // State for filters
  const [level, setLevel] = useState(levelParam);
  const [academicYear, setAcademicYear] = useState(academicYearParam);
  const [themeFilter, setThemeFilter] = useState(themeParam);
  const [university, setUniversity] = useState(universityParam);
  const [office, setOffice] = useState(officeParam);
  const [tabValue, setTabValue] = useState(0);
  const [anchorEl, setAnchorEl] = useState(null);

  // State for data
  const [academicYears, setAcademicYears] = useState([]);
  const [themes, setThemes] = useState([]);
  const [universities, setUniversities] = useState([]);
  const [offices, setOffices] = useState([]);
  const [dashboardData, setDashboardData] = useState(null);

  // State for loading and error
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Animation variants (removed for now)
  // const containerVariants = {
  //   hidden: { opacity: 0 },
  //   visible: {
  //     opacity: 1,
  //     transition: {
  //       staggerChildren: 0.1
  //     }
  //   }
  // };

  // const itemVariants = {
  //   hidden: { y: 20, opacity: 0 },
  //   visible: {
  //     y: 0,
  //     opacity: 1,
  //     transition: {
  //       type: 'spring',
  //       stiffness: 100,
  //       damping: 12
  //     }
  //   }
  // };

  // Level configuration
  const levelConfig = {
    university: {
      icon: SchoolIcon,
      color: theme.palette.primary.main,
      bgColor: theme.palette.primary.light + '20',
      label: 'University Level'
    },
    office: {
      icon: BusinessIcon,
      color: theme.palette.secondary.main,
      bgColor: theme.palette.secondary.light + '20',
      label: 'Office Level'
    },
    user: {
      icon: PersonIcon,
      color: theme.palette.info.main,
      bgColor: theme.palette.info.light + '20',
      label: 'User Level'
    }
  };

  // Helper functions
  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const refreshData = () => {
    setLoading(true);
    // Trigger data refetch by updating a dependency
    setError(null);
  };

  // Fetch filter options
  useEffect(() => {
    const fetchFilterOptions = async () => {
      try {
        // Fetch academic years
        const yearsResponse = await getAcademicYears();
        setAcademicYears(yearsResponse.results || []);

        // Set default academic year if not provided
        if (!academicYear && yearsResponse.results && yearsResponse.results.length > 0) {
          const currentYear = yearsResponse.results.find(year => year.is_current) || yearsResponse.results[0];
          setAcademicYear(currentYear.id.toString());
        }

        // Fetch themes
        const themesResponse = await getThemes();
        setThemes(themesResponse.results || []);

        // Fetch universities (only in public tenant)
        if (isPublicTenant) {
          const universitiesResponse = await getUniversities();
          setUniversities(universitiesResponse.results || []);
        }
      } catch (err) {
        console.error('Error fetching filter options:', err);
        setError('Failed to load filter options. Please try again later.');
      }
    };

    fetchFilterOptions();
  }, [academicYear, isPublicTenant]);

  // Fetch offices when university changes
  useEffect(() => {
    const fetchOffices = async () => {
      if ((isPublicTenant && university) || !isPublicTenant) {
        try {
          const params = isPublicTenant ? { university } : {};
          const officesResponse = await getOffices(params);
          setOffices(officesResponse.results || []);
        } catch (err) {
          console.error('Error fetching offices:', err);
          setError('Failed to load offices. Please try again later.');
        }
      }
    };

    if (level === 'office' || level === 'user') {
      fetchOffices();
    }
  }, [level, university, isPublicTenant]);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Prepare parameters
        const params = {
          level,
          academic_year: academicYear
        };

        if (themeFilter) {
          params.theme = themeFilter;
        }

        if (isPublicTenant && university) {
          params.university = university;
        }

        if (level === 'user' && office) {
          params.office = office;
        }

        // Update URL with query parameters
        const queryParams = new URLSearchParams();
        Object.entries(params).forEach(([key, value]) => {
          if (value) {
            queryParams.set(key, value);
          }
        });
        navigate(`${location.pathname}?${queryParams.toString()}`);

        // Fetch dashboard data
        const data = await getDashboardData(params);
        setDashboardData(data);
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError('Failed to load dashboard data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    // Only fetch data if we have the required parameters
    if (academicYear &&
        (!isPublicTenant || (isPublicTenant && (level === 'university' || university))) &&
        (level !== 'user' || office)) {
      fetchDashboardData();
    } else {
      setLoading(false);
    }
  }, [level, academicYear, themeFilter, university, office, isPublicTenant, navigate, location.pathname]);

  // Handle filter changes
  const handleLevelChange = (event) => {
    setLevel(event.target.value);
    // Reset dependent filters
    if (event.target.value === 'university') {
      setOffice('');
    }
  };

  const handleAcademicYearChange = (event) => {
    setAcademicYear(event.target.value);
  };

  const handleThemeChange = (event) => {
    setThemeFilter(event.target.value);
  };

  const handleUniversityChange = (event) => {
    setUniversity(event.target.value);
    setOffice('');
  };

  const handleOfficeChange = (event) => {
    setOffice(event.target.value);
  };

  // Get performance metrics
  const getPerformanceMetrics = () => {
    if (!dashboardData) return { total: 0, completed: 0, inProgress: 0, overdue: 0 };

    const total = dashboardData.totalKPIs || 0;
    const completed = dashboardData.completedKPIs || 0;
    const inProgress = dashboardData.inProgressKPIs || 0;
    const overdue = dashboardData.overdueKPIs || 0;

    return { total, completed, inProgress, overdue };
  };

  const metrics = getPerformanceMetrics();
  const completionRate = metrics.total > 0 ? Math.round((metrics.completed / metrics.total) * 100) : 0;

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <DashboardIcon color="primary" sx={{ fontSize: 40, mr: 2 }} />
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              KPI Reporting Dashboard
            </Typography>
            <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
              <Chip
                icon={React.createElement(levelConfig[level].icon)}
                label={levelConfig[level].label}
                sx={{
                  bgcolor: levelConfig[level].bgColor,
                  color: levelConfig[level].color,
                  fontWeight: 500,
                }}
              />
              {academicYear && (
                <Chip
                  icon={<ScheduleIcon />}
                  label={academicYears.find(y => y.id.toString() === academicYear)?.name || 'Academic Year'}
                  variant="outlined"
                />
              )}
            </Stack>
          </Box>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="Refresh Data">
            <IconButton onClick={refreshData} color="primary">
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            onClick={handleMenuOpen}
          >
            Filters
          </Button>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            disabled={!dashboardData}
          >
            Export
          </Button>
        </Box>
      </Box>

      {/* Filter Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          sx: { minWidth: 300, p: 2 }
        }}
      >
        <Typography variant="h6" sx={{ mb: 2 }}>Dashboard Filters</Typography>

        <FormControl fullWidth sx={{ mb: 2 }}>
          <InputLabel>Level</InputLabel>
          <Select value={level} onChange={handleLevelChange} label="Level">
            <MenuItem value="university">University Level</MenuItem>
            <MenuItem value="office">Office Level</MenuItem>
            <MenuItem value="user">User Level</MenuItem>
          </Select>
        </FormControl>

        <FormControl fullWidth sx={{ mb: 2 }}>
          <InputLabel>Academic Year</InputLabel>
          <Select value={academicYear || ''} onChange={handleAcademicYearChange} label="Academic Year">
            <MenuItem value="">Select Academic Year</MenuItem>
            {academicYears.map(year => (
              <MenuItem key={year.id} value={year.id}>
                {year.name} {year.is_current ? '(Current)' : ''}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <FormControl fullWidth sx={{ mb: 2 }}>
          <InputLabel>Theme (Optional)</InputLabel>
          <Select value={themeFilter || ''} onChange={handleThemeChange} label="Theme (Optional)">
            <MenuItem value="">All Themes</MenuItem>
            {themes.map(themeItem => (
              <MenuItem key={themeItem.id} value={themeItem.id}>{themeItem.name}</MenuItem>
            ))}
          </Select>
        </FormControl>

        {isPublicTenant && (
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>University</InputLabel>
            <Select value={university || ''} onChange={handleUniversityChange} label="University">
              <MenuItem value="">Select University</MenuItem>
              {universities.map(univ => (
                <MenuItem key={univ.id} value={univ.id}>{univ.name}</MenuItem>
              ))}
            </Select>
          </FormControl>
        )}

        {(level === 'office' || level === 'user') && (
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Office</InputLabel>
            <Select value={office || ''} onChange={handleOfficeChange} label="Office">
              <MenuItem value="">Select Office</MenuItem>
              {offices.map(officeItem => (
                <MenuItem key={officeItem.id} value={officeItem.id}>{officeItem.name}</MenuItem>
              ))}
            </Select>
          </FormControl>
        )}
      </Menu>

      {/* Info Paper */}
      <Paper
        sx={{
          p: 2,
          mb: 4,
          display: 'flex',
          alignItems: 'center',
          bgcolor: alpha(theme.palette.primary.main, 0.1),
          border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
          borderRadius: 2
        }}
      >
        <AssessmentIcon color="primary" sx={{ mr: 2 }} />
        <Box>
          <Typography variant="h6" color="primary.main">
            Performance Analytics Center
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Monitor KPI performance, track progress, and analyze trends across {levelConfig[level].label.toLowerCase()}
          </Typography>
        </Box>
      </Paper>

      {/* Dashboard Content */}
      {loading ? (
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 8 }}>
          <CircularProgress size={60} sx={{ mb: 2 }} />
          <Typography variant="h6" color="text.secondary">Loading dashboard data...</Typography>
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mb: 4 }}>{error}</Alert>
      ) : !dashboardData ? (
        <Alert severity="info" sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom>Please select the required filters to view the dashboard</Typography>
          {isPublicTenant && !university && <Typography>• Please select a university</Typography>}
          {level === 'user' && !office && <Typography>• Please select an office</Typography>}
        </Alert>
      ) : (
        <div>
          {/* Performance Metrics Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{
                background: `linear-gradient(135deg, ${theme.palette.primary.light}20, ${theme.palette.primary.main}10)`,
                border: `1px solid ${theme.palette.primary.light}40`,
              }}>
                <CardContent sx={{ textAlign: 'center', p: 3 }}>
                  <Avatar sx={{ bgcolor: theme.palette.primary.main, mx: 'auto', mb: 2, width: 56, height: 56 }}>
                    <AssignmentIcon sx={{ fontSize: 28 }} />
                  </Avatar>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: theme.palette.primary.main, mb: 1 }}>
                    {metrics.total}
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    Total KPIs
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{
                background: `linear-gradient(135deg, ${theme.palette.success.light}20, ${theme.palette.success.main}10)`,
                border: `1px solid ${theme.palette.success.light}40`,
              }}>
                <CardContent sx={{ textAlign: 'center', p: 3 }}>
                  <Avatar sx={{ bgcolor: theme.palette.success.main, mx: 'auto', mb: 2, width: 56, height: 56 }}>
                    <CheckCircleIcon sx={{ fontSize: 28 }} />
                  </Avatar>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: theme.palette.success.main, mb: 1 }}>
                    {metrics.completed}
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    Completed
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{
                background: `linear-gradient(135deg, ${theme.palette.warning.light}20, ${theme.palette.warning.main}10)`,
                border: `1px solid ${theme.palette.warning.light}40`,
              }}>
                <CardContent sx={{ textAlign: 'center', p: 3 }}>
                  <Avatar sx={{ bgcolor: theme.palette.warning.main, mx: 'auto', mb: 2, width: 56, height: 56 }}>
                    <ScheduleIcon sx={{ fontSize: 28 }} />
                  </Avatar>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: theme.palette.warning.main, mb: 1 }}>
                    {metrics.inProgress}
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    In Progress
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{
                background: `linear-gradient(135deg, ${theme.palette.error.light}20, ${theme.palette.error.main}10)`,
                border: `1px solid ${theme.palette.error.light}40`,
              }}>
                <CardContent sx={{ textAlign: 'center', p: 3 }}>
                  <Avatar sx={{ bgcolor: theme.palette.error.main, mx: 'auto', mb: 2, width: 56, height: 56 }}>
                    <WarningIcon sx={{ fontSize: 28 }} />
                  </Avatar>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: theme.palette.error.main, mb: 1 }}>
                    {metrics.overdue}
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    Overdue
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Completion Rate */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12}>
              <Card>
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Typography variant="h6">Overall Completion Rate</Typography>
                    <Chip
                      label={`${completionRate}%`}
                      color={completionRate >= 80 ? 'success' : completionRate >= 60 ? 'warning' : 'error'}
                      sx={{ fontWeight: 600 }}
                    />
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={completionRate}
                    sx={{
                      height: 12,
                      borderRadius: 6,
                      bgcolor: alpha(theme.palette.primary.main, 0.1),
                      '& .MuiLinearProgress-bar': {
                        borderRadius: 6,
                        background: completionRate >= 80
                          ? `linear-gradient(90deg, ${theme.palette.success.main}, ${theme.palette.success.light})`
                          : completionRate >= 60
                          ? `linear-gradient(90deg, ${theme.palette.warning.main}, ${theme.palette.warning.light})`
                          : `linear-gradient(90deg, ${theme.palette.error.main}, ${theme.palette.error.light})`
                      }
                    }}
                  />
                </CardContent>
              </Card>
            </Grid>
          </Grid>
          {/* Chart Tabs */}
          <Box sx={{ mb: 4 }}>
            <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 3 }}>
              <Tab
                icon={<ShowChartIcon />}
                label="Overview"
                sx={{ textTransform: 'none', fontWeight: 500 }}
              />
              <Tab
                icon={<BarChartIcon />}
                label="Comparison"
                sx={{ textTransform: 'none', fontWeight: 500 }}
              />
            </Tabs>

            {/* Tab Panels */}
            {tabValue === 0 && (
              <div>
                <Grid container spacing={3} sx={{ mb: 3 }}>
                  <Grid item xs={12} lg={6}>
                    <Card sx={{ height: '100%' }}>
                      <CardHeader
                        avatar={
                          <Avatar sx={{ bgcolor: theme.palette.primary.main }}>
                            <TimelineIcon />
                          </Avatar>
                        }
                        title="Performance Trend"
                        subheader="Track KPI performance over time"
                      />
                      <Divider />
                      <CardContent>
                        <PerformanceTrendChart data={dashboardData.performanceTrend} />
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid item xs={12} lg={6}>
                    <Card sx={{ height: '100%' }}>
                      <CardHeader
                        avatar={
                          <Avatar sx={{ bgcolor: theme.palette.secondary.main }}>
                            <PieChartIcon />
                          </Avatar>
                        }
                        title="KPI Status Distribution"
                        subheader="Distribution of KPIs by status"
                      />
                      <Divider />
                      <CardContent>
                        <KPIDistributionChart data={dashboardData.kpiDistribution} />
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>

                <Grid container spacing={3}>
                  <Grid item xs={12} lg={6}>
                    <Card sx={{ height: '100%' }}>
                      <CardHeader
                        avatar={
                          <Avatar sx={{ bgcolor: theme.palette.success.main }}>
                            <StarIcon />
                          </Avatar>
                        }
                        title="Top Performers"
                        subheader="Highest performing entities"
                      />
                      <Divider />
                      <CardContent>
                        <TopPerformersChart data={dashboardData.topPerformers} level={level} />
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid item xs={12} lg={6}>
                    <Card sx={{ height: '100%' }}>
                      <CardHeader
                        avatar={
                          <Avatar sx={{ bgcolor: theme.palette.warning.main }}>
                            <InfoIcon />
                          </Avatar>
                        }
                        title="Low Performers"
                        subheader="Entities needing attention"
                      />
                      <Divider />
                      <CardContent>
                        <LowPerformersChart data={dashboardData.lowPerformers} level={level} />
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </div>
            )}

            {tabValue === 1 && (
              <div>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Card>
                      <CardHeader
                        avatar={
                          <Avatar sx={{ bgcolor: theme.palette.info.main }}>
                            <BarChartIcon />
                          </Avatar>
                        }
                        title="Performance Comparison"
                        subheader="Compare performance across entities"
                      />
                      <Divider />
                      <CardContent>
                        <PerformanceComparisonChart
                          universityData={dashboardData.universityPerformance}
                          themeData={dashboardData.themePerformance}
                          officeData={dashboardData.officePerformance}
                          userData={dashboardData.userPerformance}
                          level={level}
                        />
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </div>
            )}
          </Box>
        </div>
      )}
    </Container>
  );
};

export default KPIDashboard;
