import { useNavigate, useLocation } from 'react-router-dom'
import {
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Collapse,
  Toolbar,
  useTheme,
  Typography,
} from '@mui/material'
import {
  Dashboard as DashboardIcon,
  School as SchoolIcon,
  Category as CategoryIcon,
  Assignment as AssignmentIcon,
  Settings as SettingsIcon,
  ExpandLess as ExpandLessIcon,
  ExpandMore as ExpandMoreIcon,
  Timeline as TimelineIcon,
  CalendarMonth as CalendarMonthIcon,
  Straighten as MeasurementIcon,
  BarChart,
  BarChart as KPIIcon,
  Link as LinkIcon,
  Api as ApiIcon,
  Assessment as AssessmentIcon,
  Business as BusinessIcon,
  People as PeopleIcon,
  Person as PersonIcon,
  LocalOffer as TagIcon,
  CloudUpload as CloudUploadIcon,
  FileUpload as FileUploadIcon,
  Storage as StorageIcon,
  ImportExport as ImportExportIcon,
  VerifiedUser as ValidationIcon,
  Backup as BackupIcon,
  Sync as SyncIcon,
  Preview as PreviewIcon,
  History as HistoryIcon,
  DataObject as DataToolsIcon,
  GroupAdd as GroupAddIcon,
  DoneAll as DoneAllIcon,
  MergeType as MergeTypeIcon,
} from '@mui/icons-material'
import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'

const drawerWidth = 260 // Width of the sidebar drawer
const collapsedWidth = 0

const Sidebar = ({ user, open, onClose }) => {
  const navigate = useNavigate()
  const location = useLocation()
  const theme = useTheme()

  // No state variables needed for menu sections

  // No useEffect needed for menu expansion

  const isActive = (path) => {
    return location.pathname === path
  }

  const handleNavigation = (path) => {
    navigate(path)
    if (window.innerWidth < 600) {
      onClose()
    }
  }

  // Animation variants for menu items
  const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0 }
  }

  const drawer = (
    <Box sx={{
      display: 'flex',
      flexDirection: 'column',
      height: '100%',
      bgcolor: '#f8fafc',
      p: 0, // Remove default padding
    }}>
      <Toolbar sx={{
        minHeight: '64px !important',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        p: 0, // Remove default padding
      }} />

      <Box sx={{
        flexGrow: 1,
        overflowY: 'auto',
        px: 1, // Reduce padding
        py: 1  // Reduce padding
      }}>
        <List component={motion.ul}
          initial="hidden"
          animate="visible"
          variants={{
            visible: {
              transition: {
                staggerChildren: 0.05
              }
            }
          }}
          sx={{
            p: 0, // Remove default padding
            '& .MuiListItemButton-root': {
              borderRadius: 1,
              mb: 0.5,
              py: 1,
              '&.Mui-selected': {
                bgcolor: '#1e40af',
                color: 'white',
                '&:hover': {
                  bgcolor: '#1e3a8a',
                },
                '& .MuiListItemIcon-root': {
                  color: 'white',
                }
              },
              '&:hover': {
                bgcolor: 'rgba(30, 64, 175, 0.08)',
              }
            }
          }}
        >
          {/* Dashboard Section */}
          <motion.div variants={itemVariants}>
            <ListItem disablePadding>
              <ListItemButton
                onClick={() => handleNavigation('/dashboard')}
                selected={location.pathname === '/dashboard'}
                component="a"
                href="#/dashboard"
                tabIndex={0}
                aria-label="Dashboard"
              >
                <ListItemIcon>
                  <DashboardIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Dashboard"
                  primaryTypographyProps={{
                    fontSize: '0.9rem',
                  }}
                />
              </ListItemButton>
            </ListItem>
          </motion.div>

          {/* KPI Reporting Section */}
          <motion.div variants={itemVariants}>
            <ListItem disablePadding>
              <ListItemButton
                onClick={() => handleNavigation('/reporting')}
                selected={location.pathname === '/reporting' ||
                         location.pathname.includes('/reporting/')}
              >
                <ListItemIcon>
                  <AssessmentIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="KPI Reporting"
                  primaryTypographyProps={{
                    fontSize: '0.9rem',
                  }}
                />
                {location.pathname === '/reporting' || location.pathname.includes('/reporting/') ? (
                  <ExpandLessIcon fontSize="small" />
                ) : (
                  <ExpandMoreIcon fontSize="small" />
                )}
              </ListItemButton>
            </ListItem>

            {/* Reporting Submenu */}
            <Collapse
              in={location.pathname === '/reporting' || location.pathname.includes('/reporting/')}
              timeout="auto"
              unmountOnExit
            >
              <List component="div" disablePadding>
                <ListItemButton
                  sx={{ pl: 4 }}
                  onClick={() => handleNavigation('/reporting/workflow')}
                  selected={location.pathname === '/reporting/workflow'}
                >
                  <ListItemIcon>
                    <TimelineIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Workflow"
                    primaryTypographyProps={{
                      fontSize: '0.85rem',
                    }}
                  />
                </ListItemButton>

                <ListItemButton
                  sx={{ pl: 4 }}
                  onClick={() => handleNavigation('/reporting/calendar')}
                  selected={location.pathname === '/reporting/calendar'}
                >
                  <ListItemIcon>
                    <CalendarMonthIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Calendar"
                    primaryTypographyProps={{
                      fontSize: '0.85rem',
                    }}
                  />
                </ListItemButton>

                <ListItemButton
                  sx={{ pl: 4 }}
                  onClick={() => handleNavigation('/reporting/batch-review')}
                  selected={location.pathname === '/reporting/batch-review'}
                >
                  <ListItemIcon>
                    <DoneAllIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Batch Review"
                    primaryTypographyProps={{
                      fontSize: '0.85rem',
                    }}
                  />
                </ListItemButton>

                <ListItemButton
                  sx={{ pl: 4 }}
                  onClick={() => handleNavigation('/reporting/aggregate')}
                  selected={location.pathname === '/reporting/aggregate'}
                >
                  <ListItemIcon>
                    <MergeTypeIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Aggregate"
                    primaryTypographyProps={{
                      fontSize: '0.85rem',
                    }}
                  />
                </ListItemButton>

                <ListItemButton
                  sx={{ pl: 4 }}
                  onClick={() => handleNavigation('/reporting/dashboard')}
                  selected={location.pathname === '/reporting/dashboard'}
                >
                  <ListItemIcon>
                    <BarChart fontSize="small" />
                  </ListItemIcon>
                  <ListItemText
                    primary="KPI Dashboard"
                    primaryTypographyProps={{
                      fontSize: '0.85rem',
                    }}
                  />
                </ListItemButton>
              </List>
            </Collapse>
          </motion.div>

          {/* KPI Assignment Section */}
          <motion.div variants={itemVariants}>
            <ListItem disablePadding>
              <ListItemButton
                onClick={() => handleNavigation('/assignments')}
                selected={location.pathname === '/assignments' || location.pathname.startsWith('/assignments/')}
                sx={{
                  bgcolor: location.pathname.startsWith('/assignments') ? 'rgba(25, 118, 210, 0.08)' : 'transparent',
                  '&:hover': {
                    bgcolor: 'rgba(25, 118, 210, 0.12)',
                  }
                }}
              >
                <ListItemIcon>
                  <AssignmentIcon fontSize="small" color={location.pathname.startsWith('/assignments') ? 'primary' : 'inherit'} />
                </ListItemIcon>
                <ListItemText
                  primary="KPI Assignments"
                  primaryTypographyProps={{
                    fontSize: '0.9rem',
                    fontWeight: location.pathname.startsWith('/assignments') ? 'medium' : 'normal',
                    color: location.pathname.startsWith('/assignments') ? 'primary.main' : 'inherit'
                  }}
                />
              </ListItemButton>
            </ListItem>
          </motion.div>

          {/* KPI Definitions Section */}
          <motion.div variants={itemVariants}>
            <ListItem disablePadding>
              <ListItemButton
                onClick={() => handleNavigation('/kpi-definition')}
                selected={location.pathname === '/kpi-definition' ||
                         location.pathname.includes('/themes') ||
                         location.pathname.includes('/subthemes') ||
                         location.pathname.includes('/academic-years') ||
                         location.pathname.includes('/measurement-units') ||
                         location.pathname.includes('/kpis') ||
                         location.pathname.includes('/import')}
              >
                <ListItemIcon>
                  <CategoryIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="KPI Definition"
                  primaryTypographyProps={{
                    fontSize: '0.9rem',
                  }}
                />
              </ListItemButton>
            </ListItem>
          </motion.div>

          {/* Data Tools Section */}
          <motion.div variants={itemVariants}>
            <ListItem disablePadding>
              <ListItemButton
                onClick={() => handleNavigation('/data-tools')}
                selected={location.pathname === '/data-tools' ||
                         location.pathname.includes('/data-tools')}
              >
                <ListItemIcon>
                  <DataToolsIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Data Tools"
                  primaryTypographyProps={{
                    fontSize: '0.9rem',
                  }}
                />
              </ListItemButton>
            </ListItem>
          </motion.div>

          {/* Settings Section */}
          <motion.div variants={itemVariants}>
            <ListItem disablePadding>
              <ListItemButton
                onClick={() => handleNavigation('/settings')}
                selected={location.pathname.includes('/settings') ||
                         location.pathname.includes('/api-docs') ||
                         location.pathname.includes('/offices') ||
                         location.pathname.includes('/users')}
              >
                <ListItemIcon>
                  <SettingsIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Settings"
                  primaryTypographyProps={{
                    fontSize: '0.9rem',
                  }}
                />
              </ListItemButton>
            </ListItem>
          </motion.div>
        </List>
      </Box>
    </Box>
  )

  return (
    <Box
      component="nav"
      sx={{
        width: { sm: open ? drawerWidth : 0 }, // Dynamic width based on open state
        flexShrink: { sm: 0 },
        transition: theme => theme.transitions.create('width', {
          easing: theme.transitions.easing.sharp,
          duration: theme.transitions.duration.enteringScreen,
        }),
      }}
    >
      {/* Mobile drawer */}
      <Drawer
        variant="temporary"
        open={open}
        onClose={onClose}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile
        }}
        sx={{
          display: { xs: 'block', sm: 'none' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: drawerWidth,
            borderRight: 'none',
            boxShadow: '0 4px 25px rgba(0,0,0,0.08)',
            background: '#f8fafc',
            borderRadius: 0, // Remove border radius
            overflowX: 'hidden',
            p: 0, // Remove default padding
          },
        }}
      >
        {drawer}
      </Drawer>

      {/* Desktop drawer */}
      <Drawer
        variant="persistent"
        open={open}
        // Fix accessibility issue by not using aria-hidden
        hideBackdrop={true}
        ModalProps={{
          disableEnforceFocus: true,
          disablePortal: true
        }}
        sx={{
          display: { xs: 'none', sm: 'block' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            borderRight: 'none',
            boxShadow: '0 4px 25px rgba(0,0,0,0.08)',
            background: '#f8fafc',
            overflowX: 'hidden',
            p: 0, // Remove default padding
            transition: theme => theme.transitions.create('width', {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.enteringScreen,
            }),
            width: open ? drawerWidth : 0, // Dynamic width based on open state
            borderRadius: 0, // Remove border radius
          },
        }}
      >
        {drawer}
      </Drawer>
    </Box>
  )
}

export default Sidebar




